func (m *default{{.upperStartCamelObject}}Model) Update(ctx context.Context, session sqlx.Session, {{if .containsIndexCache}}newData{{else}}data{{end}} *{{.upperStartCamelObject}}) (sql.Result, error) {
    {{if .withCache}}{{if .containsIndexCache}}data, err := m.FindOne(ctx, newData.{{.upperStartCamelPrimaryKey}})
    if err != nil {
        return nil, err
    }{{end}}

    {{.keys}}

    return m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
        query := fmt.Sprintf("update %s set %s where {{.originalPrimaryKey}} = {{if .postgreSql}}$1{{else}}?{{end}}", m.table, {{.lowerStartCamelObject}}RowsWithPlaceHolder)
        if session != nil{
            return session.ExecCtx(ctx, query, {{.expressionValues}})
        }
        return conn.ExecCtx(ctx, query, {{.expressionValues}})
    }, {{.keyValues}}){{else}}query := fmt.Sprintf("update %s set %s where {{.originalPrimaryKey}} = {{if .postgreSql}}$1{{else}}?{{end}}", m.table, {{.lowerStartCamelObject}}RowsWithPlaceHolder)
    if session != nil{
        return session.ExecCtx(ctx, query, {{.expressionValues}})
    }
    return m.conn.ExecCtx(ctx, query, {{.expressionValues}}){{end}}
}
