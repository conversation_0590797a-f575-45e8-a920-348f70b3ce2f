// Copyright 2023 Buf Technologies, Inc.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package buf.validate.conformance.cases.other_package;

import "buf/validate/validate.proto";

// Validate message embedding across packages.
message Embed {
  message DoubleEmbed {
    enum DoubleEnumerated {
      DOUBLE_ENUMERATED_UNSPECIFIED = 0;
      DOUBLE_ENUMERATED_VALUE = 1;
    }
  }

  int64 val = 1 [(buf.validate.field).int64.gt = 0];

  enum Enumerated {
    ENUMERATED_UNSPECIFIED = 0;
    ENUMERATED_VALUE = 1;
  }
}
