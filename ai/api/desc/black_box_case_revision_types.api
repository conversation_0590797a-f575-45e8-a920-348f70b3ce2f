syntax = "v1"

import "types.api"

type BlackBoxCaseRevision {
	Id                          int           `json:"id" zh:"自增Id"`
	CaseId                      string        `json:"case_id" zh:"用例Id"`
	RevisionId                  string        `json:"revision_id" zh:"版本Id"`
	RevisionName                string        `json:"revision_name" zh:"版本名称"`
	KnowledgeId                 string        `json:"knowledge_id" zh:"知识文档关联ID"`
	KnowledgeParagraphTitleText string        `json:"knowledge_paragraph_title_text" zh:"知识文档段落标题"`
	KnowledgeParagraphTitle    []string        `json:"knowledge_paragraph_title" zh:"知识文档段落标题列表"`
	CaseRefId					string        `json:"case_ref_id" zh:"用例关联ID"`
	CreatedBy                   *FullUserInfo `json:"created_by" zh:"创建者的用户ID"`
	UpdatedBy                   *FullUserInfo `json:"updated_by" zh:"最近一次更新者的用户ID"`
	DeletedBy                   *FullUserInfo `json:"deleted_by" zh:"删除者的用户ID"`
	CreatedAt                   int64         `json:"created_at" zh:"创建时间"`
	UpdatedAt                   int64         `json:"updated_at" zh:"更新时间"`
	DeletedAt                   int64         `json:"deleted_at" zh:"删除时间"`
}

type (
	ListBlackBoxCaseRevisionReq {
		CaseId string `json:"case_id" zh:"用例Id"`
	}
	ListBlackBoxCaseRevisionResp {
		Items []*BlackBoxCaseRevision `json:"items"`
	}

	CreateBlackBoxCaseRevisionDataReq {
		CaseId string `json:"case_id" zh:"用例Id"`
		Items []*BlackBoxCaseDataBase `json:"items"`
	}
	CreateBlackBoxCaseRevisionDataResp {
		RevisionId string `json:"revision_id" zh:"版本Id"`
	}

	AdoptBlackBoxCaseRevisionDataReq {
		RevisionId string `json:"revision_id" zh:"版本Id"`
	}
	AdoptBlackBoxCaseRevisionDataResp {}
)