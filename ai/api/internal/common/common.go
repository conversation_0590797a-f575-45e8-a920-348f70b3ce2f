package common

import (
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/public/httpc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/ai/rpc/pb"
)

func MergeKnowledgeSupplementDoc(documents []*pb.BlackBoxCaseMapDocument) (supplementDocs []httpc.SupplementDoc) {
	var (
		err error
	)

	supplementDocs = make([]httpc.SupplementDoc, 0)
	supplementMergeDocsMap := make(map[string][]*httpc.KnowledgeDocPgTitle) // key: knowledgeDocId

	for _, document := range documents {
		expeDoc := &types.BlackBoxCaseKnowledgeSupplementDoc{}
		if document.GetExpeDoc() != "" {
			err = jsonx.Unmarshal([]byte(document.GetExpeDoc()), expeDoc)
			if err != nil {
				logx.Error("MergeKnowledgeSupplementDoc expeDoc jsonx.Unmarshal error: ", err)
				return supplementDocs
			}
			if expeDoc.Type == "markdown" {
				supplementDocs = append(
					supplementDocs, httpc.SupplementDoc{
						Type: "markdown",
						Markdown: httpc.Markdown{
							Text: expeDoc.Markdown.Text,
						},
					},
				)
			} else {
				var knowledgeDocPgTitle []*httpc.KnowledgeDocPgTitle
				for _, titleMap := range expeDoc.Feishu.KnowledgeDocPgTitle {
					knowledgeDocPgTitle = append(
						knowledgeDocPgTitle, &httpc.KnowledgeDocPgTitle{
							Id:    titleMap.Id,
							Title: titleMap.Title,
						},
					)
				}
				supplementMergeDocsMap[expeDoc.Feishu.KnowledgeDocId] = append(
					supplementMergeDocsMap[expeDoc.Feishu.KnowledgeDocId],
					knowledgeDocPgTitle...,
				)
			}
		}

		funcDoc := &types.BlackBoxCaseKnowledgeSupplementDoc{}
		if document.GetFuncDoc() != "" {
			err = jsonx.Unmarshal([]byte(document.GetFuncDoc()), funcDoc)
			if err != nil {
				logx.Error("MergeKnowledgeSupplementDoc funcDoc jsonx.Unmarshal error: ", err)
				return supplementDocs
			}
			if funcDoc.Type == "markdown" {
				supplementDocs = append(
					supplementDocs, httpc.SupplementDoc{
						Type: "markdown",
						Markdown: httpc.Markdown{
							Text: funcDoc.Markdown.Text,
						},
					},
				)
			} else {
				var knowledgeDocPgTitle []*httpc.KnowledgeDocPgTitle
				for _, titleMap := range funcDoc.Feishu.KnowledgeDocPgTitle {
					knowledgeDocPgTitle = append(
						knowledgeDocPgTitle, &httpc.KnowledgeDocPgTitle{
							Id:    titleMap.Id,
							Title: titleMap.Title,
						},
					)
				}
				supplementMergeDocsMap[funcDoc.Feishu.KnowledgeDocId] = append(
					supplementMergeDocsMap[funcDoc.Feishu.KnowledgeDocId],
					knowledgeDocPgTitle...,
				)
			}
		}
	}

	for key, docs := range supplementMergeDocsMap {
		supplementDoc := httpc.SupplementDoc{}
		docs := common.RemoveDuplicate[*httpc.KnowledgeDocPgTitle](docs)
		supplementDoc.Type = "feishu"
		supplementDoc.Feishu = httpc.FeishuDoc{
			KnowledgeDocId:      key,
			KnowledgeDocPgTitle: docs,
		}
		supplementDocs = append(supplementDocs, supplementDoc)
	}

	return supplementDocs
}

func KnowledgeDocPgTitleReq2PbStruct(knowledgeDocPgTitles []types.KnowledgeDocPgTitle) (res []*pb.KnowledgeDocPgTitle) {
	res = make([]*pb.KnowledgeDocPgTitle, 0, len(knowledgeDocPgTitles))
	for _, title := range knowledgeDocPgTitles {
		res = append(res, &pb.KnowledgeDocPgTitle{
			Id:           title.Id,
			Title:        title.Title,
			DemandPoints: title.DemandPoints,
		})
	}
	return res
}

func KnowledgeDocPgTitlePbStruct2Req(knowledgeDocPgTitles []*pb.KnowledgeDocPgTitle) (res []*types.KnowledgeDocPgTitle) {
	res = make([]*types.KnowledgeDocPgTitle, 0, len(knowledgeDocPgTitles))
	for _, title := range knowledgeDocPgTitles {
		res = append(res, &types.KnowledgeDocPgTitle{
			Id:           title.Id,
			Title:        title.Title,
			DemandPoints: title.DemandPoints,
		})
	}
	return res
}
