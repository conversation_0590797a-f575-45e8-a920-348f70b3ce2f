USE artificial_intelligence;
CREATE TABLE `black_box_case_assistant`
(
    `id`                    int                                    NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`            varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
    `assistant_id`          varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '助手ID',
    `assistant_name`        varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '助手名称',
    `assistant_description` varchar(255) COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '助手描述',
    `account`               varchar(64) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '用户id【仅在个人空间存在此数据】',
    `work_space_type`       tinyint                                NOT NULL DEFAULT '0' COMMENT '工作空间类型：个人空间(private)：0，公共空间()：1',
    `work_priority_type`    tinyint                                NOT NULL DEFAULT '0' COMMENT '工作优先级类型：准确优先：0，速度优先：1',
    `session_round`         int                                    NOT NULL DEFAULT '0' COMMENT '对话轮数',
    `document_ids`          varchar(5120) COLLATE utf8mb4_general_ci        DEFAULT NULL COMMENT '文档集合',
    `deleted`               tinyint                                NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`            varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
    `updated_by`            varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`            varchar(64) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '删除者的用户ID',
    `created_at`            timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`            timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`            timestamp                              NULL     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_bbca_piai` (`project_id`, `assistant_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='AI-黑盒测试用例-测试助手表';

CREATE TABLE `black_box_case_assistant_document_relationship`
(
    `id`           int                                    NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`   varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
    `assistant_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '助手ID',
    `document_id`  varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档ID',
    `deleted`      tinyint                                NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`   varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
    `updated_by`   varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`   varchar(64) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '删除者的用户ID',
    `created_at`   timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`   timestamp                              NULL     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_bbcadr_piai` (`project_id`, `assistant_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='AI-黑盒测试用例-测试助手文档关联表';

CREATE TABLE `black_box_case_assistant_document_session_relationship`
(
    `id`           int                                    NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`   varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
    `assistant_id` varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '助手ID',
    `session_id`   varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '会话ID',
    `document_id`  varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档ID',
    `deleted`      tinyint                                NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`   varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
    `updated_by`   varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`   varchar(64) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '删除者的用户ID',
    `created_at`   timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`   timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`   timestamp                              NULL     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_bbcadsr_piaisi` (`project_id`, `assistant_id`, `session_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='AI-黑盒测试用例-测试助手文档会话关联表';

CREATE TABLE `black_box_case_document`
(
    `id`                   int                                    NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`           varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
    `assistant_id`         varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '助手ID',
    `document_id`          varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档ID',
    `document_name`        varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '文档名称',
    `document_description` varchar(255) COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '文档描述',
    `document_url`         varchar(255) COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '文档地址',
    `document_text`        varchar(255) COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '文档文本',
    `document_type`        tinyint                                NOT NULL DEFAULT '0' COMMENT '文档类型：纯文本(text)：0，飞书(feishu)：1，doc(doc)：2',
    `external_document_id` varchar(64) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '外部文档ID',
    `status`               tinyint                                NOT NULL DEFAULT '0' COMMENT '文档状态：处理中：0，完成：1，失败：2',
    `deleted`              tinyint                                NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`           varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
    `updated_by`           varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`           varchar(64) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '删除者的用户ID',
    `created_at`           timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`           timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`           timestamp                              NULL     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_bbcd_piaidi` (`project_id`, `assistant_id`, `document_id`),
    KEY `idx_bbcd_piai` (`project_id`, `assistant_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='AI-黑盒测试用例-测试文档表';

CREATE TABLE `black_box_case_session`
(
    `id`                  int                                    NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `project_id`          varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目ID',
    `assistant_id`        varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '助手ID',
    `session_id`          varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '会话ID',
    `external_session_id` varchar(64) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '外部会话ID',
    `session_name`        varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '会话名称',
    `session_description` varchar(255) COLLATE utf8mb4_general_ci         DEFAULT NULL COMMENT '会话描述',
    `session_round`       int                                    NOT NULL DEFAULT '0' COMMENT '对话轮数[继承助手配置轮数]',
    `account`             varchar(64) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '用户id【仅在个人空间存在此数据】',
    `document_ids`        varchar(5120) COLLATE utf8mb4_general_ci        DEFAULT NULL COMMENT '文档集合[继承助手文档的子集]',
    `status`              tinyint                                NOT NULL DEFAULT '0' COMMENT '会话状态：使用中：0，归档：1',
    `deleted`             tinyint                                NOT NULL DEFAULT '0' COMMENT '逻辑删除标识（未删除、已删除）',
    `created_by`          varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '创建者的用户ID',
    `updated_by`          varchar(64) COLLATE utf8mb4_general_ci NOT NULL COMMENT '最近一次更新者的用户ID',
    `deleted_by`          varchar(64) COLLATE utf8mb4_general_ci          DEFAULT NULL COMMENT '删除者的用户ID',
    `created_at`          timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at`          timestamp                              NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted_at`          timestamp                              NULL     DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_bbcs_piaisi` (`project_id`, `assistant_id`, `session_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT ='AI-黑盒测试用例-测试会话表';

