CREATE
DATABASE IF NOT EXISTS `dispatcher` DEFAULT CHARSET = utf8mb4 COLLATE utf8mb4_0900_ai_ci;

USE
`dispatcher`;
create table dispatcher.task_info_record
(
    id                   int auto_increment comment '自增id'
        primary key,
    project_id           varchar(64)                           not null comment '项目id',
    plan_id              varchar(64)                           not null comment '计划id',
    plan_name            varchar(512)                          not null comment '计划名称',
    trigger_mode         varchar(64) default ''                not null comment '触发模式',
    task_id              varchar(64)                           not null comment '任务id',
    priority_type        tinyint     default 1                 not null comment '优先级策略',
    task_execute_status  tinyint     default 0 null comment '0排队中,1执行中,2已完成,3已停止',
    task_executed_result tinyint     default 0 null comment '执行结果(1成功,2失败,3异常)',

    total_case           int         default 0                 not null comment '测试用例总数',
    finished_case        int         default 0                 not null comment '执行完成的测试用例数',
    success_case         int         default 0                 not null comment '执行成功的测试用例数',

    total_suite          int         default 0                 not null comment '测试集合总数',
    finished_suite       int         default 0                 not null comment '执行完的测试集合数',
    success_suite        int         default 0                 not null comment '执行成功的测试集合数',

    executed_by          varchar(64)                           not null comment '执行人id',

    created_at           timestamp   default CURRENT_TIMESTAMP not null comment '创建时间(戳)',
    cost_time            bigint      default 0 null comment '执行耗时(豪秒)',
    wait_time            bigint      default 0 null comment '排队耗时',
    started_at           bigint                                not null comment '开始执行的时间(戳)',
    ended_at             bigint null comment '结束执行的时间(戳)',
) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT ='设备表';

create index idx_tir_pipi
    on dispatcher.task_info_record (project_id, plan_id);
