package config

import (
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/producer"
)

type Config struct {
	zrpc.RpcServerConf

	DB        types.DBConfig
	RedisConf redis.RedisConf
	Cache     cache.CacheConf

	Manager  zrpc.RpcClientConf
	Reporter zrpc.RpcClientConf
	User     zrpc.RpcClientConf

	ApiWorkerProducer       producer.Config
	UIWorkerProducer        producer.Config
	PerfWorkerProducer      producer.Config
	DispatcherProducer      producer.Config
	TaskConsumer            consumer.Config
	BeatConsumer            consumer.Config
	WorkerProducer          producer.Config
	StabilityWorkerProducer producer.Config
}

func (c Config) ListenOn() string {
	return c.RpcServerConf.ListenOn
}

func (c Config) LogConfig() logx.LogConf {
	return c.RpcServerConf.ServiceConf.Log
}
