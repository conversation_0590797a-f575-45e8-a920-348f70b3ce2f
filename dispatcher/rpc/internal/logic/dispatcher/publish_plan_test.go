package dispatcherlogic

import (
	"context"
	"testing"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/rpc/pb"
	managerpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

func TestPublishPlan(t *testing.T) {
	// 这个方法还没测试过！！！！！！！！！！！！！！！！！！！！！！！！
	projectId := utils.GenProjectId()
	req := &pb.PublishReq{
		TriggerMode: commonpb.TriggerMode_MANUAL,
		ProjectId:   projectId,
		ExecuteType: managerpb.ApiExecutionDataType_API_PLAN,
		PublishType: pb.PublishType_PublishType_API_PLAN,
		Data: &pb.PublishReq_Plan{
			Plan: &pb.PlanPublishInfo{
				PlanId: "plan_id:vj6vAQUZ7vXeXUzs3V2Oy",
			},
		},
	}

	logic := NewPublishLogic(context.Background(), svc.Mock_ServiceContext())
	_, err := logic.Publish(req)
	if err != nil {
		t.Errorf("%s", err)
		t.FailNow()
	}
}
