package handler

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/cronscheduler"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/handler/task_7day_check"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/handler/task_invalid_check"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/dispatcher/beat/internal/svc"
)

func RegisterHandlers(server *cronscheduler.Scheduler, serverCtx *svc.ServiceContext) error {
	return server.RegisterTasks(
		map[string]func(){
			"*/10 * * * *": task_invalid_check.CreateHandler(serverCtx),
			"0 3 * * *":    task_7day_check.CreateHandler(serverCtx),
		},
	)
}
