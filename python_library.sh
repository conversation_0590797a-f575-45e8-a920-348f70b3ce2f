#!/bin/sh

# 接收构建参数 ALPINE_IMAGE
ALPINE_IMAGE="$1"

IMAGE_NAME="python"
# 检查是否有python环境
result=$(echo $ALPINE_IMAGE | grep "${IMAGE_NAME}")
if [[ "$result" != "" ]]
then
    echo "$IMAGE_NAME 字段存在：$ALPINE_IMAGE"
    pip install --timeout 30 --user --no-cache-dir --no-warn-script-location RSA
    pip install --timeout 30 --user --no-cache-dir --no-warn-script-location pymysql
else
    echo "$IMAGE_NAME 字段不存在：$ALPINE_IMAGE"
fi

