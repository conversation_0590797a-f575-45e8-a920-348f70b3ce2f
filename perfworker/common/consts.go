package common

import "time"

const (
	DefaultPeriodOfWatchStopSignal     = 5 * time.Second
	DefaultPeriodOfWatchServiceMetrics = 30 * time.Second
	DefaultTimeoutOfSendTask           = 2 * time.Second
	DefaultTimeoutOfInvokeRPC          = 5 * time.Second
	DefaultTimeoutOfReleaseAccounts    = 2 * time.Minute

	MaxTimeoutOfWaitJobStarted = 10 * time.Minute
	IntervalOfCheckJobStarted  = 5 * time.Second
)

type ErrMsgZh string

const (
	// perfworker [https://q9jvw0u5f5.feishu.cn/wiki/GheVwLKwLi7gdIk8nxDcg2U4nxd#share-Q9L6diw8PotUyFxomghcraydnjb]
	ErrMsgZh_Worker_OthersFailed           ErrMsgZh = "请联系平台人员处理"   // 其他的失败
	ErrMsgZh_Worker_PVCCheckFailed         ErrMsgZh = "PVC资源检查失败"   // PVC资源检查失败
	ErrMsgZh_Worker_PVCNotFound            ErrMsgZh = "PVC资源不存在"    // PVC资源不存在
	ErrMsgZh_Worker_ProtoPullFailed        ErrMsgZh = "Proto工程拉取失败" // Proto工程拉取失败
	ErrMsgZh_Worker_PerfDataGenFailed      ErrMsgZh = "压测数据生成失败"    // 压测数据生成失败
	ErrMsgZh_Worker_AccountPullFailed      ErrMsgZh = "池账号获取失败"     // 池账号获取失败
	ErrMsgZh_Worker_AccountUnavailable     ErrMsgZh = "没有可用的池账号"    // 没有可用的池账号
	ErrMsgZh_Worker_JobCreateFailed        ErrMsgZh = "Job资源创建失败"   // Job资源创建失败
	ErrMsgZh_Worker_PerfTaskSendFailed     ErrMsgZh = "压测任务发送失败"    // 压测任务发送失败
	ErrMsgZh_Worker_PerfResultReportFailed ErrMsgZh = "压测结果上报失败"    // 压测结果上报失败
	ErrMsgZh_Worker_JobWaitCompletedFailed ErrMsgZh = "Job资源等待完成失败" // Job资源等待完成失败
	ErrMsgZh_Worker_PerfWorkerShutdown     ErrMsgZh = "Worker终止"    // Worker终止

	// perftool [https://q9jvw0u5f5.feishu.cn/wiki/GheVwLKwLi7gdIk8nxDcg2U4nxd#share-Cup0deoUboHHBUxYinEcVSCmn6e]
	ErrMsgZh_Tool_OthersFailed          ErrMsgZh = "请查看日志失败原因"  // 其他的失败
	ErrMsgZh_Tool_AllClientCreateFailed ErrMsgZh = "全部客户端创建失败"  // 全部客户端创建失败
	ErrMsgZh_Tool_AllVULoginFailed      ErrMsgZh = "全部虚拟用户登录失败" // 全部虚拟用户登录失败
)
