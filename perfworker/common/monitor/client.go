package monitor

import (
	"context"
	"fmt"
	"math"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/pkg/errors"
	promv1 "github.com/prometheus/client_golang/api/prometheus/v1"
	"github.com/prometheus/client_golang/prometheus"
	prommodel "github.com/prometheus/common/model"
	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/metrics"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/monitor"
	reporterpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

type Client struct {
	conf   monitor.Config
	client *monitor.Client
}

func NewMonitorClient(conf monitor.Config) *Client {
	return &Client{
		conf:   conf,
		client: monitor.MustNewClient(conf),
	}
}

func (c *Client) QueryAPIMetrics(
	ctx context.Context, taskID string, startedAt, endedAt time.Time,
) ([]*reporterpb.APIMetric, error) {
	q := &Query{
		Logger: logx.WithContext(ctx),

		c:         c,
		ctx:       ctx,
		taskID:    taskID,
		startedAt: startedAt,
		endedAt:   endedAt,
	}

	amCache := make(apiMetricCache, constants.ConstDefaultMakeMapSize)
	if err := q.queryRangeCountOfCounter(amCache); err != nil {
		return nil, err
	}

	rrsCache := make(responseResultsCache, constants.ConstDefaultMakeMapSize)
	if err := q.queryRangeCountOfHistogram(rrsCache); err != nil {
		return nil, err
	}
	if err := q.queryRangeSumOfHistogram(rrsCache); err != nil {
		return nil, err
	}
	if err := q.queryRangeBucketOfHistogram(rrsCache); err != nil {
		return nil, err
	}
	// 屏蔽分位数指标
	//if err := q.queryRangeQuantileOfHistogram(rrsCache); err != nil {
	//	return nil, err
	//}

	keys := make([]prommodel.LabelValue, 0, len(amCache))
	for key := range amCache {
		keys = append(keys, key)
	}

	slices.Sort(keys)
	apiMetrics := make([]*reporterpb.APIMetric, 0, len(keys))
	for _, key := range keys {
		am, ok := amCache[key]
		if !ok {
			continue
		} else if am.ApiName == "" {
			continue
		}
		apiMetrics = append(apiMetrics, am)

		rrCache, ok := rrsCache[key]
		if !ok {
			continue
		}

		ks := make([]prommodel.LabelValue, 0, len(rrCache))
		for k := range rrCache {
			ks = append(ks, k)
		}

		slices.Sort(ks)
		am.RespSuites = make([]*reporterpb.APIMetric_ResponseResult, 0, len(ks))
		for _, k := range ks {
			rr, ok := rrCache[k]
			if !ok {
				continue
			} else if rr.Result == "" {
				continue
			}

			am.RespSuites = append(am.RespSuites, rr)
		}
	}

	return apiMetrics, nil
}

type Query struct {
	logx.Logger

	c *Client

	ctx                context.Context
	taskID             string
	startedAt, endedAt time.Time
}

func (q *Query) query(query string, ts time.Time) (prommodel.Value, error) {
	q.Infof(
		"prepare to call the query api by monitor client, promQL: %s, time: %s(%d)",
		query, ts.Format("2006-01-02 15:04:05"), ts.UnixMilli(),
	)

	value, warnings, err := q.c.client.Query(q.ctx, query, ts, monitor.WithTimeout(timeout))
	if err != nil {
		return value, err
	}
	if len(warnings) > 0 {
		q.Warnf(
			"got some warnings while calling the query api by monitor client, warnings: %s",
			jsonx.MarshalIgnoreError(warnings),
		)
	}

	q.Debugf("got a value from calling the query api by monitor client, promQL: %s, value: %s", query, value.String())
	return value, nil
}

func (q *Query) queryRange(query string, from, to time.Time) (prommodel.Value, error) {
	q.Infof(
		"prepare to call the query range api by monitor client, promQL: %s, from: %s(%d), to: %s(%d)",
		query, from.Format("2006-01-02 15:04:05"), from.UnixMilli(),
		to.Format("2006-01-02 15:04:05"), to.UnixMilli(),
	)

	value, warnings, err := q.c.client.QueryRange(
		q.ctx, query, promv1.Range{
			Start: from,
			End:   to,
			Step:  30 * time.Second,
		}, monitor.WithTimeout(timeout),
	)
	if err != nil {
		return value, err
	}
	if len(warnings) > 0 {
		q.Warnf(
			"got some warnings while calling the query range api by monitor client, warnings: %s",
			jsonx.MarshalIgnoreError(warnings),
		)
	}

	q.Debugf(
		"got a value from calling the query range api by monitor client, promQL: %s, value: %s", query, value.String(),
	)
	return value, nil
}

func (q *Query) getCountQueryOfCounter() string {
	fqName := prometheus.BuildFQName(namespace, subsystem, metrics.ConstMetricsPerfToolRequestTotal)
	return fmt.Sprintf(
		`sum(%s{app="%s", %s="%s"}) by (%s, %s)`,
		fqName, perftool, metrics.ConstMetricsLabelNameOfPerfTaskTaskID, q.taskID,
		metrics.ConstMetricsLabelNameOfPerfTaskName, metrics.ConstMetricsLabelNameOfPerfTaskResult,
	)
}

func (q *Query) queryCountOfCounter(cache apiMetricCache) error {
	query := q.getCountQueryOfCounter()
	value, err := q.query(query, q.endedAt)
	if err != nil {
		return err
	}

	vector, ok := value.(prommodel.Vector)
	if !ok {
		return errors.Errorf(
			"got an invalid value type from the query api by monitor client, promQL: %s, type: %s, value: %s",
			query, value.Type(), value.String(),
		)
	} else if len(vector) == 0 {
		return q.queryRangeCountOfCounter(cache)
	}

	for _, sample := range vector {
		if sample.Metric == nil {
			continue
		}

		name, ok := sample.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
		if !ok {
			continue
		}
		result, ok := sample.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
		if !ok {
			continue
		}

		am, ok := cache[name]
		if !ok {
			am = &reporterpb.APIMetric{
				ApiName: string(name),
			}
			cache[name] = am
		}

		switch string(result) {
		case metrics.ConstMetricsLabelValueOfRequestFailed:
			am.ReqFailed = int64(sample.Value)
		case metrics.ConstMetricsLabelValueOfRequestSucceeded:
			am.ReqSuccessful = int64(sample.Value)
		}
	}

	return nil
}

func (q *Query) queryRangeCountOfCounter(cache apiMetricCache) error {
	query := q.getCountQueryOfCounter()
	value, err := q.queryRange(query, q.startedAt, q.endedAt)
	if err != nil {
		return err
	}

	matrix, ok := value.(prommodel.Matrix)
	if !ok {
		return errors.Errorf(
			"got an invalid value type from the query range api by monitor client, promQL: %s, type: %s, value: %s",
			query, value.Type(), value.String(),
		)
	}

	for _, stream := range matrix {
		if stream.Metric == nil {
			continue
		}

		name, ok := stream.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
		if !ok {
			continue
		}
		result, ok := stream.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
		if !ok {
			continue
		}

		am, ok := cache[name]
		if !ok {
			am = &reporterpb.APIMetric{
				ApiName: string(name),
			}
			cache[name] = am
		}

		switch r := string(result); r {
		case metrics.ConstMetricsLabelValueOfRequestFailed, metrics.ConstMetricsLabelValueOfRequestSucceeded:
			for i := len(stream.Values) - 1; i >= 0; i-- {
				val := stream.Values[i]
				if math.IsNaN(float64(val.Value)) {
					continue
				}

				if strings.EqualFold(r, metrics.ConstMetricsLabelValueOfRequestFailed) {
					if am.ReqFailed < int64(val.Value) {
						am.ReqFailed = int64(val.Value)
					}
				} else if strings.EqualFold(r, metrics.ConstMetricsLabelValueOfRequestSucceeded) {
					if am.ReqSuccessful < int64(val.Value) {
						am.ReqSuccessful = int64(val.Value)
					}
				}
			}
		default:
			continue
		}
	}

	return nil
}

func (q *Query) getCountQueryOfHistogram() string {
	fqName := prometheus.BuildFQName(namespace, subsystem, metrics.ConstMetricsPerfToolRequestDuration+countSuffix)
	return fmt.Sprintf(
		`sum(%s{app="%s", %s="%s"}) by (%s, %s)`,
		fqName, perftool, metrics.ConstMetricsLabelNameOfPerfTaskTaskID, q.taskID,
		metrics.ConstMetricsLabelNameOfPerfTaskName,
		metrics.ConstMetricsLabelNameOfPerfTaskResult,
	)
}

func (q *Query) queryCountOfHistogram(cache responseResultsCache) error {
	query := q.getCountQueryOfHistogram()
	value, err := q.query(query, q.endedAt)
	if err != nil {
		return err
	}

	vector, ok := value.(prommodel.Vector)
	if !ok {
		return errors.Errorf(
			"got an invalid value type from the query api by monitor client, promQL: %s, type: %s, value: %s",
			query, value.Type(), value.String(),
		)
	} else if len(vector) == 0 {
		return q.queryRangeCountOfHistogram(cache)
	}

	for _, sample := range vector {
		if sample.Metric == nil {
			continue
		}

		name, ok := sample.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
		if !ok {
			continue
		}
		result, ok := sample.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
		if !ok {
			continue
		}

		rrs, ok := cache[name]
		if !ok {
			rrs = make(responseResultCache, constants.ConstDefaultMakeMapSize)
			cache[name] = rrs
		}
		rr, ok := rrs[result]
		if !ok {
			rr = &reporterpb.APIMetric_ResponseResult{
				Result: string(result),
			}
			rrs[result] = rr
		}

		rr.Count = uint64(sample.Value)
	}

	return nil
}

func (q *Query) queryRangeCountOfHistogram(cache responseResultsCache) error {
	query := q.getCountQueryOfHistogram()
	value, err := q.queryRange(query, q.startedAt, q.endedAt)
	if err != nil {
		return err
	}

	matrix, ok := value.(prommodel.Matrix)
	if !ok {
		return errors.Errorf(
			"got an invalid value type from the query range api by monitor client, promQL: %s, type: %s, value: %s",
			query, value.Type(), value.String(),
		)
	}

	for _, stream := range matrix {
		if stream.Metric == nil {
			continue
		}

		name, ok := stream.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
		if !ok {
			continue
		}
		result, ok := stream.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
		if !ok {
			continue
		}

		rrs, ok := cache[name]
		if !ok {
			rrs = make(responseResultCache, constants.ConstDefaultMakeMapSize)
			cache[name] = rrs
		}
		rr, ok := rrs[result]
		if !ok {
			rr = &reporterpb.APIMetric_ResponseResult{
				Result: string(result),
			}
			rrs[result] = rr
		}

		for i := len(stream.Values) - 1; i >= 0; i-- {
			val := stream.Values[i]
			if !math.IsNaN(float64(val.Value)) && rr.Count < uint64(val.Value) {
				rr.Count = uint64(val.Value)
			}
		}
	}

	return nil
}

func (q *Query) getSumQueryOfHistogram() string {
	fqName := prometheus.BuildFQName(namespace, subsystem, metrics.ConstMetricsPerfToolRequestDuration+sumSuffix)
	return fmt.Sprintf(
		`sum(%s{app="%s", %s="%s"}) by (%s, %s)`,
		fqName, perftool, metrics.ConstMetricsLabelNameOfPerfTaskTaskID, q.taskID,
		metrics.ConstMetricsLabelNameOfPerfTaskName, metrics.ConstMetricsLabelNameOfPerfTaskResult,
	)
}

func (q *Query) querySumOfHistogram(cache responseResultsCache) error {
	query := q.getSumQueryOfHistogram()
	value, err := q.query(query, q.endedAt)
	if err != nil {
		return err
	}

	vector, ok := value.(prommodel.Vector)
	if !ok {
		return errors.Errorf(
			"got an invalid value type from the query api by monitor client, promQL: %s, type: %s, value: %s",
			query, value.Type(), value.String(),
		)
	} else if len(vector) == 0 {
		return q.queryRangeSumOfHistogram(cache)
	}

	for _, sample := range vector {
		if sample.Metric == nil {
			continue
		}

		name, ok := sample.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
		if !ok {
			continue
		}
		result, ok := sample.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
		if !ok {
			continue
		}

		rrs, ok := cache[name]
		if !ok {
			rrs = make(responseResultCache, constants.ConstDefaultMakeMapSize)
			cache[name] = rrs
		}
		rr, ok := rrs[result]
		if !ok {
			rr = &reporterpb.APIMetric_ResponseResult{
				Result: string(result),
			}
			rrs[result] = rr
		}

		rr.Sum = float64(sample.Value)
	}

	return nil
}

func (q *Query) queryRangeSumOfHistogram(cache responseResultsCache) error {
	query := q.getSumQueryOfHistogram()
	value, err := q.queryRange(query, q.startedAt, q.endedAt)
	if err != nil {
		return err
	}

	matrix, ok := value.(prommodel.Matrix)
	if !ok {
		return errors.Errorf(
			"got an invalid value type from the query range api by monitor client, promQL: %s, type: %s, value: %s",
			query, value.Type(), value.String(),
		)
	}

	for _, stream := range matrix {
		if stream.Metric == nil {
			continue
		}

		name, ok := stream.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
		if !ok {
			continue
		}
		result, ok := stream.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
		if !ok {
			continue
		}

		rrs, ok := cache[name]
		if !ok {
			rrs = make(responseResultCache, constants.ConstDefaultMakeMapSize)
			cache[name] = rrs
		}
		rr, ok := rrs[result]
		if !ok {
			rr = &reporterpb.APIMetric_ResponseResult{
				Result: string(result),
			}
			rrs[result] = rr
		}

		for i := len(stream.Values) - 1; i >= 0; i-- {
			val := stream.Values[i]
			if !math.IsNaN(float64(val.Value)) && rr.Sum < float64(val.Value) {
				rr.Sum = float64(val.Value)
			}
		}
	}

	return nil
}

func (q *Query) getBucketQueryOfHistogram() string {
	fqName := prometheus.BuildFQName(namespace, subsystem, metrics.ConstMetricsPerfToolRequestDuration+bucketSuffix)
	return fmt.Sprintf(
		`sum(%s{app="%s", %s="%s"}) by (%s, %s, %s)`,
		fqName, perftool, metrics.ConstMetricsLabelNameOfPerfTaskTaskID, q.taskID,
		metrics.ConstMetricsLabelNameOfPerfTaskName, metrics.ConstMetricsLabelNameOfPerfTaskResult,
		metrics.ConstMetricsLabelNameOfLe,
	)
}

func (q *Query) queryBucketOfHistogram(cache responseResultsCache) error {
	query := q.getBucketQueryOfHistogram()
	value, err := q.query(query, q.endedAt)
	if err != nil {
		return err
	}

	vector, ok := value.(prommodel.Vector)
	if !ok {
		return errors.Errorf(
			"got an invalid value type from the query api by monitor client, promQL: %s, type: %s, value: %s",
			query, value.Type(), value.String(),
		)
	} else if len(vector) == 0 {
		return q.queryRangeBucketOfHistogram(cache)
	}

	slices.SortFunc(vector, sortFuncOfSample)
	for _, sample := range vector {
		if sample.Metric == nil {
			continue
		}

		name, ok := sample.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
		if !ok {
			continue
		}
		result, ok := sample.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
		if !ok {
			continue
		}
		le, ok := sample.Metric[metrics.ConstMetricsLabelNameOfLe]
		if !ok {
			continue
		}

		rrs, ok := cache[name]
		if !ok {
			rrs = make(responseResultCache, constants.ConstDefaultMakeMapSize)
			cache[name] = rrs
		}
		rr, ok := rrs[result]
		if !ok {
			rr = &reporterpb.APIMetric_ResponseResult{
				Result: string(result),
			}
			rrs[result] = rr
		}

		rr.Buckets = append(
			rr.Buckets, &reporterpb.APIMetric_ResponseResult_KeyValuePair{
				Key:   "LE " + string(le),
				Value: sample.Value.String(),
			},
		)
	}

	return nil
}

func (q *Query) queryRangeBucketOfHistogram(cache responseResultsCache) error {
	query := q.getBucketQueryOfHistogram()
	value, err := q.queryRange(query, q.startedAt, q.endedAt)
	if err != nil {
		return err
	}

	matrix, ok := value.(prommodel.Matrix)
	if !ok {
		return errors.Errorf(
			"got an invalid value type from the query range api by monitor client, promQL: %s, type: %s, value: %s",
			query, value.Type(), value.String(),
		)
	}

	slices.SortFunc(matrix, sortFuncOfSampleStream)
	for _, stream := range matrix {
		if stream.Metric == nil {
			continue
		}

		name, ok := stream.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
		if !ok {
			continue
		}
		result, ok := stream.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
		if !ok {
			continue
		}
		le, ok := stream.Metric[metrics.ConstMetricsLabelNameOfLe]
		if !ok {
			continue
		}

		rrs, ok := cache[name]
		if !ok {
			rrs = make(responseResultCache, constants.ConstDefaultMakeMapSize)
			cache[name] = rrs
		}
		rr, ok := rrs[result]
		if !ok {
			rr = &reporterpb.APIMetric_ResponseResult{
				Result: string(result),
			}
			rrs[result] = rr
		}

		var leVal prommodel.SampleValue
		for i := len(stream.Values) - 1; i >= 0; i-- {
			val := stream.Values[i]
			if !math.IsNaN(float64(val.Value)) && leVal < val.Value {
				leVal = val.Value
			}
		}
		rr.Buckets = append(
			rr.Buckets, &reporterpb.APIMetric_ResponseResult_KeyValuePair{
				Key:   "LE " + string(le),
				Value: leVal.String(),
			},
		)
	}

	return nil
}

func (q *Query) getQuantileQueryOfHistogram(percentile float64) string {
	fqName := prometheus.BuildFQName(namespace, subsystem, metrics.ConstMetricsPerfToolRequestDuration+bucketSuffix)
	return fmt.Sprintf(
		`histogram_quantile(%.2f, sum(irate(%s{app="%s", %s="%s"}[1m])) by (%s, %s, %s))`,
		percentile, fqName, perftool, metrics.ConstMetricsLabelNameOfPerfTaskTaskID, q.taskID,
		metrics.ConstMetricsLabelNameOfPerfTaskName, metrics.ConstMetricsLabelNameOfPerfTaskResult,
		metrics.ConstMetricsLabelNameOfLe,
	)
}

func (q *Query) queryRangeQuantileOfHistogram(cache responseResultsCache) error {
	for _, percentile := range metrics.DefaultMetricsPercentiles {
		query := q.getQuantileQueryOfHistogram(percentile)
		value, err := q.queryRange(query, q.startedAt, q.endedAt)
		if err != nil {
			return err
		}

		matrix, ok := value.(prommodel.Matrix)
		if !ok {
			return errors.Errorf(
				"got an invalid value type from the query api by monitor client, promQL: %s, type: %s, value: %s",
				query, value.Type(), value.String(),
			)
		}

		for _, stream := range matrix {
			if stream.Metric == nil {
				continue
			}

			name, ok := stream.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
			if !ok {
				continue
			}
			result, ok := stream.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
			if !ok {
				continue
			}

			rrs, ok := cache[name]
			if !ok {
				rrs = make(responseResultCache, constants.ConstDefaultMakeMapSize)
				cache[name] = rrs
			}
			rr, ok := rrs[result]
			if !ok {
				rr = &reporterpb.APIMetric_ResponseResult{
					Result: string(result),
				}
				rrs[result] = rr
			}

			for i := len(stream.Values) - 1; i >= 0; i-- {
				val := stream.Values[i]
				if !math.IsNaN(float64(val.Value)) {
					rr.Quantiles = append(
						rr.Quantiles, &reporterpb.APIMetric_ResponseResult_KeyValuePair{
							Key:   strconv.FormatFloat(percentile*100, 'f', 0, 64) + "%",
							Value: strconv.FormatFloat(float64(val.Value), 'f', 2, 64),
						},
					)
					break
				}
			}
		}
	}

	return nil
}

func sortFuncOfSample(a, b *prommodel.Sample) int {
	aName, ok := a.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
	if !ok {
		return -1
	}
	bName, ok := b.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
	if !ok {
		return 1
	}
	if r := strings.Compare(string(aName), string(bName)); r != 0 {
		return r
	}

	aResult, ok := a.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
	if !ok {
		return -1
	}
	bResult, ok := b.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
	if !ok {
		return 1
	}
	if r := strings.Compare(string(aResult), string(bResult)); r != 0 {
		return r
	}

	aLe, ok := a.Metric[metrics.ConstMetricsLabelNameOfLe]
	if !ok {
		return -1
	}
	bLe, ok := b.Metric[metrics.ConstMetricsLabelNameOfLe]
	if !ok {
		return 1
	}

	if string(aLe) == inf {
		return 1
	} else if string(bLe) == inf {
		return -1
	}

	aLeFloat, err := strconv.ParseFloat(string(aLe), 64)
	if err != nil {
		return -1
	}
	bLeFloat, err := strconv.ParseFloat(string(bLe), 64)
	if err != nil {
		return 1
	}

	if aLeFloat == bLeFloat {
		return 0
	}
	if aLeFloat < bLeFloat {
		return -1
	}
	return 1
}

func sortFuncOfSampleStream(a, b *prommodel.SampleStream) int {
	aName, ok := a.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
	if !ok {
		return -1
	}
	bName, ok := b.Metric[metrics.ConstMetricsLabelNameOfPerfTaskName]
	if !ok {
		return 1
	}
	if r := strings.Compare(string(aName), string(bName)); r != 0 {
		return r
	}

	aResult, ok := a.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
	if !ok {
		return -1
	}
	bResult, ok := b.Metric[metrics.ConstMetricsLabelNameOfPerfTaskResult]
	if !ok {
		return 1
	}
	if r := strings.Compare(string(aResult), string(bResult)); r != 0 {
		return r
	}

	aLe, ok := a.Metric[metrics.ConstMetricsLabelNameOfLe]
	if !ok {
		return -1
	}
	bLe, ok := b.Metric[metrics.ConstMetricsLabelNameOfLe]
	if !ok {
		return 1
	}

	if string(aLe) == inf {
		return 1
	} else if string(bLe) == inf {
		return -1
	}

	aLeFloat, err := strconv.ParseFloat(string(aLe), 64)
	if err != nil {
		return -1
	}
	bLeFloat, err := strconv.ParseFloat(string(bLe), 64)
	if err != nil {
		return 1
	}

	if aLeFloat == bLeFloat {
		return 0
	}
	if aLeFloat < bLeFloat {
		return -1
	}
	return 1
}
