package relationservicelogic

import (
	"testing"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/permission/common/mock"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/relation/rpc/pb"
)

func Test_GetServiceBindTestCaseLogic(t *testing.T) {
	logic := NewGetServiceBindTestCaseLogic(mock.Mock_Context(), svc.Mock_ServiceContext())
	resp, err := logic.GetServiceBindTestCase(
		&pb.GetServiceBindTestCaseRequest{
			ProjectId:       "project_id:Kqllt5-9fA-I5UOdhjA5d",
			GeneralConfigId: "general_config_id:HjKRqD6mKoel5Zh_ZEOyi",
			ServiceNames:    []string{"channel-convene"},
		},
	)
	if err != nil {
		t.Fatal(err)
	}

	t.Logf("relations = %s", protobuf.MarshalJSONToStringIgnoreError(resp.GetRelations()))
}
