package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/common"
)

type (
	Device         = common.Device
	DeviceMetadata = common.DeviceMetadata
)

type SearchDeviceReq struct {
	Condition  *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort       []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchDeviceResp struct {
	CurrentPage uint64    `json:"current_page"`
	PageSize    uint64    `json:"page_size"`
	TotalCount  uint64    `json:"total_count"`
	TotalPage   uint64    `json:"total_page"`
	Items       []*Device `json:"items"`
}

type AcquireDeviceReq struct {
	UDID       string `json:"udid" validate:"required" copier:"Udid" zh:"设备编号"`
	Expiration uint32 `json:"expiration,omitempty,optional" zh:"最大占用时长（秒）"`
}

type AcquireDeviceResp struct {
	*Device
}

type SearchAcquireDeviceReq struct {
	Condition  *api.Condition `json:"condition,omitempty,optional" zh:"查询条件"`
	Count      uint32         `json:"count,default=1" zh:"申请占用的设备数量"`
	Expiration uint32         `json:"expiration,omitempty,optional" zh:"最大占用时长（秒）"`
}

type SearchAcquireDeviceResp struct {
	Devices []*Device `json:"devices"`
}

type ReleaseDeviceReq struct {
	UDID  string `json:"udid" validate:"required" copier:"Udid" zh:"设备编号"`
	Token string `json:"token" validate:"required" zh:"令牌"`
}

type ReleaseDeviceResp struct{}
