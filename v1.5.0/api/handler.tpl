package {{.PkgName}}

import (
    "net/http"

    "github.com/pkg/errors"
    "github.com/zeromicro/go-zero/rest/httpx"

    "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
    "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/response"
    {{.ImportPackages}}
)

func {{.HandlerName}}(svcCtx *svc.ServiceContext) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        {{if .HasRequest}}var req types.{{.RequestType}}
        if err := httpx.Parse(r, &req); err != nil {
            response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ParseParamError, err.Error()), "failed to parse parameters, error: %+v", err))
            return
        }

        if err := svcCtx.Validator.Validate.StructCtx(r.Context(), req); err != nil {
            response.MakeHttpResponse(r, w, nil, errors.Wrapf(errorx.Err(errorx.ValidateParamError, svcCtx.Validator.Translate(err)), "failed to validate parameters, error: %+v", err))
            return
        }

        {{end}}l := {{.LogicName}}.New{{.LogicType}}(r.Context(), svcCtx)
        {{if .HasResp}}resp, {{end}}err := l.{{.Call}}({{if .HasRequest}}&req{{end}})
        response.MakeHttpResponse(r, w, resp, err)
    }
}
