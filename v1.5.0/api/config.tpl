package config

import (
    "fmt"

    "github.com/zeromicro/go-zero/core/logx"
    {{.authImport}}

    "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

type Config struct {
    rest.RestConf
    {{.auth}}
    {{.jwtTrans}}

    Validator types.ValidatorConfig
}

func (c Config) ListenOn() string {
    return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

func (c Config) LogConfig() logx.LogConf {
	return c.RestConf.ServiceConf.Log
}
