package svc

import (
    {{.configImport}}

    "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
)

type ServiceContext struct {
    Config {{.config}}
    {{.middleware}}

    Validator *utils.Validator
}

func NewServiceContext(c {{.config}}) *ServiceContext {
    return &ServiceContext{
        Config: c,
        {{.middlewareAssignment}}
        Validator: utils.NewValidator(c.Validator.Locale),
    }
}
