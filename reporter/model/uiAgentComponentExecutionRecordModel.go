package model

import (
	"context"

	"github.com/Masterminds/squirrel"

	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
	_ UiAgentComponentExecutionRecordModel = (*customUiAgentComponentExecutionRecordModel)(nil)

	uiAgentComponentExecutionRecordInsertFields = stringx.Remove(uiAgentComponentExecutionRecordFieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// UiAgentComponentExecutionRecordModel is an interface to be customized, add more methods here,
	// and implement the added methods in customUiAgentComponentExecutionRecordModel.
	UiAgentComponentExecutionRecordModel interface {
		uiAgentComponentExecutionRecordModel
		types.DBModel

		withSession(session sqlx.Session) UiAgentComponentExecutionRecordModel

		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *UiAgentComponentExecutionRecord) squirrel.InsertBuilder
		UpdateBuilder(data *UiAgentComponentExecutionRecord) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*UiAgentComponentExecutionRecord, error)
	}

	customUiAgentComponentExecutionRecordModel struct {
		*defaultUiAgentComponentExecutionRecordModel

		conn sqlx.SqlConn
	}
)

// NewUiAgentComponentExecutionRecordModel returns a model for the database table.
func NewUiAgentComponentExecutionRecordModel(conn sqlx.SqlConn) UiAgentComponentExecutionRecordModel {
	return &customUiAgentComponentExecutionRecordModel{
		defaultUiAgentComponentExecutionRecordModel: newUiAgentComponentExecutionRecordModel(conn),
		conn: conn,
	}
}

func (m *customUiAgentComponentExecutionRecordModel) withSession(session sqlx.Session) UiAgentComponentExecutionRecordModel {
	return NewUiAgentComponentExecutionRecordModel(sqlx.NewSqlConnFromSession(session))
}

func (m *customUiAgentComponentExecutionRecordModel) Table() string {
	return m.table
}

func (m *customUiAgentComponentExecutionRecordModel) Fields() []string {
	return uiAgentComponentExecutionRecordFieldNames
}

func (m *customUiAgentComponentExecutionRecordModel) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return m.conn.TransactCtx(ctx, fn)
}

func (m *customUiAgentComponentExecutionRecordModel) InsertBuilder(data *UiAgentComponentExecutionRecord) squirrel.InsertBuilder {
	// TODO: fill the insert values
	return squirrel.Insert(m.table).Columns(uiAgentComponentExecutionRecordInsertFields...).Values()
}

func (m *customUiAgentComponentExecutionRecordModel) UpdateBuilder(data *UiAgentComponentExecutionRecord) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
		// TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *customUiAgentComponentExecutionRecordModel) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select(uiAgentComponentExecutionRecordFieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiAgentComponentExecutionRecordModel) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *customUiAgentComponentExecutionRecordModel) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = m.conn.QueryRowCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *customUiAgentComponentExecutionRecordModel) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*UiAgentComponentExecutionRecord, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*UiAgentComponentExecutionRecord
	err = m.conn.QueryRowsCtx(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
