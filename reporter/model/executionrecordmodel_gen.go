// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	executionRecordTableName           = "`execution_record`"
	executionRecordFieldNames          = builder.RawFieldNames(&ExecutionRecord{})
	executionRecordRows                = strings.Join(executionRecordFieldNames, ",")
	executionRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(executionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	executionRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(executionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	executionRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ExecutionRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ExecutionRecord, error)
		FindOneByTaskIdProjectIdExecuteIdComponentExecuteIdTimesComponentType(ctx context.Context, taskId string, projectId string, executeId string, componentExecuteId string, times int64, componentType string) (*ExecutionRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *ExecutionRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultExecutionRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	ExecutionRecord struct {
		Id                       int64          `db:"id"`                          // 自增id
		TaskId                   string         `db:"task_id"`                     // 任务id
		ProjectId                string         `db:"project_id"`                  // 项目id
		ExecuteId                string         `db:"execute_id"`                  // 执行id
		ExecuteType              string         `db:"execute_type"`                // 执行类型
		GeneralConfig            sql.NullString `db:"general_config"`              // 通用配置
		AccountConfig            sql.NullString `db:"account_config"`              // 池账号配置
		ComponentId              string         `db:"component_id"`                // 组件id
		ComponentName            string         `db:"component_name"`              // 组件名称
		ComponentType            string         `db:"component_type"`              // 组件类型
		ComponentExecuteId       string         `db:"component_execute_id"`        // 组件执行id
		ParentComponentId        sql.NullString `db:"parent_component_id"`         // 父组件id
		ParentComponentExecuteId sql.NullString `db:"parent_component_execute_id"` // 父组件执行id
		Version                  string         `db:"version"`                     // 组件版本
		Times                    int64          `db:"times"`                       // 第几次执行（兼容循环组件）
		Status                   sql.NullString `db:"status"`                      // 执行状态（结果）
		Content                  sql.NullString `db:"content"`                     // 执行数据详情
		IsRoot                   int64          `db:"is_root"`                     // 是否是根节点, 0:不是根节点； 1:是跟节点
		ExecutedBy               string         `db:"executed_by"`                 // 执行人
		StartedAt                int64          `db:"started_at"`                  // 开始执行的时间(戳)
		EndedAt                  sql.NullInt64  `db:"ended_at"`                    // 结束执行的时间(戳)
		CostTime                 int64          `db:"cost_time"`                   // 执行耗时(毫秒)
		Callback                 sql.NullString `db:"callback"`                    // callback日志
		CreatedAt                time.Time      `db:"created_at"`                  // 创建时间(戳)
		UpdatedAt                time.Time      `db:"updated_at"`                  // 更新时间(戳)
		Cleaned                  int64          `db:"cleaned"`                     // 是否已清理过记录
		MaintainedBy             string         `db:"maintained_by"`               // 负责人
	}
)

func newExecutionRecordModel(conn sqlx.SqlConn) *defaultExecutionRecordModel {
	return &defaultExecutionRecordModel{
		conn:  conn,
		table: "`execution_record`",
	}
}

func (m *defaultExecutionRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultExecutionRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultExecutionRecordModel) FindOne(ctx context.Context, id int64) (*ExecutionRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", executionRecordRows, m.table)
	var resp ExecutionRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultExecutionRecordModel) FindOneByTaskIdProjectIdExecuteIdComponentExecuteIdTimesComponentType(ctx context.Context, taskId string, projectId string, executeId string, componentExecuteId string, times int64, componentType string) (*ExecutionRecord, error) {
	var resp ExecutionRecord
	query := fmt.Sprintf("select %s from %s where `task_id` = ? and `project_id` = ? and `execute_id` = ? and `component_execute_id` = ? and `times` = ? and `component_type` = ? and `deleted` = ? limit 1", executionRecordRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, taskId, projectId, executeId, componentExecuteId, times, componentType, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultExecutionRecordModel) Insert(ctx context.Context, session sqlx.Session, data *ExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, executionRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig, data.ComponentId, data.ComponentName, data.ComponentType, data.ComponentExecuteId, data.ParentComponentId, data.ParentComponentExecuteId, data.Version, data.Times, data.Status, data.Content, data.IsRoot, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned, data.MaintainedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig, data.ComponentId, data.ComponentName, data.ComponentType, data.ComponentExecuteId, data.ParentComponentId, data.ParentComponentExecuteId, data.Version, data.Times, data.Status, data.Content, data.IsRoot, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned, data.MaintainedBy)
}

func (m *defaultExecutionRecordModel) Update(ctx context.Context, session sqlx.Session, newData *ExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, executionRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.ProjectId, newData.ExecuteId, newData.ExecuteType, newData.GeneralConfig, newData.AccountConfig, newData.ComponentId, newData.ComponentName, newData.ComponentType, newData.ComponentExecuteId, newData.ParentComponentId, newData.ParentComponentExecuteId, newData.Version, newData.Times, newData.Status, newData.Content, newData.IsRoot, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.Callback, newData.Cleaned, newData.MaintainedBy, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.ProjectId, newData.ExecuteId, newData.ExecuteType, newData.GeneralConfig, newData.AccountConfig, newData.ComponentId, newData.ComponentName, newData.ComponentType, newData.ComponentExecuteId, newData.ParentComponentId, newData.ParentComponentExecuteId, newData.Version, newData.Times, newData.Status, newData.Content, newData.IsRoot, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.Callback, newData.Cleaned, newData.MaintainedBy, newData.Id)
}

func (m *defaultExecutionRecordModel) tableName() string {
	return m.table
}
