// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfPlanExecutionRecordTableName           = "`perf_plan_execution_record`"
	perfPlanExecutionRecordFieldNames          = builder.RawFieldNames(&PerfPlanExecutionRecord{})
	perfPlanExecutionRecordRows                = strings.Join(perfPlanExecutionRecordFieldNames, ",")
	perfPlanExecutionRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(perfPlanExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfPlanExecutionRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(perfPlanExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	perfPlanExecutionRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfPlanExecutionRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfPlanExecutionRecord, error)
		FindOneByTaskIdExecuteIdProjectId(ctx context.Context, taskId string, executeId string, projectId string) (*PerfPlanExecutionRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfPlanExecutionRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfPlanExecutionRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	PerfPlanExecutionRecord struct {
		Id             int64          `db:"id"`              // 自增ID
		TaskId         string         `db:"task_id"`         // 任务ID
		ExecuteId      string         `db:"execute_id"`      // 执行ID
		ProjectId      string         `db:"project_id"`      // 项目ID
		PlanId         string         `db:"plan_id"`         // 计划ID
		PlanName       string         `db:"plan_name"`       // 计划名称
		TriggerMode    string         `db:"trigger_mode"`    // 触发模式
		TargetMaxRps   int64          `db:"target_max_rps"`  // 目标最大的RPS
		TargetDuration int64          `db:"target_duration"` // 目标压测持续时长（单位为秒）
		Protocol       string         `db:"protocol"`        // 协议
		TargetEnv      string         `db:"target_env"`      // 目标环境（开发环境、测试环境、预发布环境、灰度环境、生产环境）
		Status         sql.NullString `db:"status"`          // 执行状态（结果）
		TaskType       string         `db:"task_type"`       // 任务类型（执行、调试）
		ExecutionMode  string         `db:"execution_mode"`  // 执行方式（按时长、按次数）
		Services       sql.NullString `db:"services"`        // 计划涉及的服务的元数据
		CostTime       int64          `db:"cost_time"`       // 执行耗时（单位为毫秒）
		MonitorUrls    sql.NullString `db:"monitor_urls"`    // 监控面板地址
		ExecutedBy     string         `db:"executed_by"`     // 执行者的用户ID
		StartedAt      sql.NullTime   `db:"started_at"`      // 开始时间
		EndedAt        sql.NullTime   `db:"ended_at"`        // 结束时间
		ApiMetrics     sql.NullString `db:"api_metrics"`     // 计划涉及的接口的指标信息
		ErrMsg         sql.NullString `db:"err_msg"`         // 计划执行错误信息
		Cleaned        int64          `db:"cleaned"`         // 清理标识（未清理、已清理）
		Deleted        int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		CreatedBy      string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy      string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy      sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt      time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt      time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt      sql.NullTime   `db:"deleted_at"`      // 删除时间
	}
)

func newPerfPlanExecutionRecordModel(conn sqlx.SqlConn) *defaultPerfPlanExecutionRecordModel {
	return &defaultPerfPlanExecutionRecordModel{
		conn:  conn,
		table: "`perf_plan_execution_record`",
	}
}

func (m *defaultPerfPlanExecutionRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultPerfPlanExecutionRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultPerfPlanExecutionRecordModel) FindOne(ctx context.Context, id int64) (*PerfPlanExecutionRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfPlanExecutionRecordRows, m.table)
	var resp PerfPlanExecutionRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfPlanExecutionRecordModel) FindOneByTaskIdExecuteIdProjectId(ctx context.Context, taskId string, executeId string, projectId string) (*PerfPlanExecutionRecord, error) {
	var resp PerfPlanExecutionRecord
	query := fmt.Sprintf("select %s from %s where `task_id` = ? and `execute_id` = ? and `project_id` = ? and `deleted` = ? limit 1", perfPlanExecutionRecordRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, taskId, executeId, projectId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfPlanExecutionRecordModel) Insert(ctx context.Context, session sqlx.Session, data *PerfPlanExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfPlanExecutionRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TargetMaxRps, data.TargetDuration, data.Protocol, data.TargetEnv, data.Status, data.TaskType, data.ExecutionMode, data.Services, data.CostTime, data.MonitorUrls, data.ExecutedBy, data.StartedAt, data.EndedAt, data.ApiMetrics, data.ErrMsg, data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TargetMaxRps, data.TargetDuration, data.Protocol, data.TargetEnv, data.Status, data.TaskType, data.ExecutionMode, data.Services, data.CostTime, data.MonitorUrls, data.ExecutedBy, data.StartedAt, data.EndedAt, data.ApiMetrics, data.ErrMsg, data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
}

func (m *defaultPerfPlanExecutionRecordModel) Update(ctx context.Context, session sqlx.Session, newData *PerfPlanExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfPlanExecutionRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.ProjectId, newData.PlanId, newData.PlanName, newData.TriggerMode, newData.TargetMaxRps, newData.TargetDuration, newData.Protocol, newData.TargetEnv, newData.Status, newData.TaskType, newData.ExecutionMode, newData.Services, newData.CostTime, newData.MonitorUrls, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.ApiMetrics, newData.ErrMsg, newData.Cleaned, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.ProjectId, newData.PlanId, newData.PlanName, newData.TriggerMode, newData.TargetMaxRps, newData.TargetDuration, newData.Protocol, newData.TargetEnv, newData.Status, newData.TaskType, newData.ExecutionMode, newData.Services, newData.CostTime, newData.MonitorUrls, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.ApiMetrics, newData.ErrMsg, newData.Cleaned, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
}

func (m *defaultPerfPlanExecutionRecordModel) tableName() string {
	return m.table
}
