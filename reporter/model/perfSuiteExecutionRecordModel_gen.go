// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	perfSuiteExecutionRecordTableName           = "`perf_suite_execution_record`"
	perfSuiteExecutionRecordFieldNames          = builder.RawFieldNames(&PerfSuiteExecutionRecord{})
	perfSuiteExecutionRecordRows                = strings.Join(perfSuiteExecutionRecordFieldNames, ",")
	perfSuiteExecutionRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(perfSuiteExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	perfSuiteExecutionRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(perfSuiteExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	perfSuiteExecutionRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PerfSuiteExecutionRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PerfSuiteExecutionRecord, error)
		FindOneByTaskIdExecuteIdProjectId(ctx context.Context, taskId string, executeId string, projectId string) (*PerfSuiteExecutionRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *PerfSuiteExecutionRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPerfSuiteExecutionRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	PerfSuiteExecutionRecord struct {
		Id            int64          `db:"id"`              // 自增ID
		TaskId        string         `db:"task_id"`         // 任务ID
		ExecuteId     string         `db:"execute_id"`      // 执行ID
		PlanExecuteId string         `db:"plan_execute_id"` // 计划执行ID
		ProjectId     string         `db:"project_id"`      // 项目ID
		SuiteId       string         `db:"suite_id"`        // 集合ID
		SuiteName     string         `db:"suite_name"`      // 集合名称
		Status        sql.NullString `db:"status"`          // 执行状态（结果）
		CostTime      int64          `db:"cost_time"`       // 执行耗时（单位为毫秒）
		ExecutedBy    string         `db:"executed_by"`     // 执行者的用户ID
		StartedAt     sql.NullTime   `db:"started_at"`      // 开始时间
		EndedAt       sql.NullTime   `db:"ended_at"`        // 结束时间
		ErrMsg        sql.NullString `db:"err_msg"`         // 集合执行错误信息
		Cleaned       int64          `db:"cleaned"`         // 清理标识（未清理、已清理）
		Deleted       int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`      // 删除时间
	}
)

func newPerfSuiteExecutionRecordModel(conn sqlx.SqlConn) *defaultPerfSuiteExecutionRecordModel {
	return &defaultPerfSuiteExecutionRecordModel{
		conn:  conn,
		table: "`perf_suite_execution_record`",
	}
}

func (m *defaultPerfSuiteExecutionRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultPerfSuiteExecutionRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultPerfSuiteExecutionRecordModel) FindOne(ctx context.Context, id int64) (*PerfSuiteExecutionRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", perfSuiteExecutionRecordRows, m.table)
	var resp PerfSuiteExecutionRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfSuiteExecutionRecordModel) FindOneByTaskIdExecuteIdProjectId(ctx context.Context, taskId string, executeId string, projectId string) (*PerfSuiteExecutionRecord, error) {
	var resp PerfSuiteExecutionRecord
	query := fmt.Sprintf("select %s from %s where `task_id` = ? and `execute_id` = ? and `project_id` = ? and `deleted` = ? limit 1", perfSuiteExecutionRecordRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, taskId, executeId, projectId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPerfSuiteExecutionRecordModel) Insert(ctx context.Context, session sqlx.Session, data *PerfSuiteExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, perfSuiteExecutionRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.PlanExecuteId, data.ProjectId, data.SuiteId, data.SuiteName, data.Status, data.CostTime, data.ExecutedBy, data.StartedAt, data.EndedAt, data.ErrMsg, data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.PlanExecuteId, data.ProjectId, data.SuiteId, data.SuiteName, data.Status, data.CostTime, data.ExecutedBy, data.StartedAt, data.EndedAt, data.ErrMsg, data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
}

func (m *defaultPerfSuiteExecutionRecordModel) Update(ctx context.Context, session sqlx.Session, newData *PerfSuiteExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, perfSuiteExecutionRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.PlanExecuteId, newData.ProjectId, newData.SuiteId, newData.SuiteName, newData.Status, newData.CostTime, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.ErrMsg, newData.Cleaned, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.PlanExecuteId, newData.ProjectId, newData.SuiteId, newData.SuiteName, newData.Status, newData.CostTime, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.ErrMsg, newData.Cleaned, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
}

func (m *defaultPerfSuiteExecutionRecordModel) tableName() string {
	return m.table
}
