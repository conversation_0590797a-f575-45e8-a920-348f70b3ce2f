// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	planExecutionRecordTableName           = "`plan_execution_record`"
	planExecutionRecordFieldNames          = builder.RawFieldNames(&PlanExecutionRecord{})
	planExecutionRecordRows                = strings.Join(planExecutionRecordFieldNames, ",")
	planExecutionRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(planExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	planExecutionRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(planExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	planExecutionRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *PlanExecutionRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*PlanExecutionRecord, error)
		FindOneByTaskIdProjectIdExecuteIdPlanExecuteId(ctx context.Context, taskId string, projectId string, executeId string, planExecuteId string) (*PlanExecutionRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *PlanExecutionRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultPlanExecutionRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	PlanExecutionRecord struct {
		Id                  int64          `db:"id"`                    // 自增id
		TaskId              string         `db:"task_id"`               // 任务id
		ProjectId           string         `db:"project_id"`            // 项目id
		ExecuteId           string         `db:"execute_id"`            // 执行id
		ExecuteType         string         `db:"execute_type"`          // 执行类型
		GeneralConfig       sql.NullString `db:"general_config"`        // 通用配置
		AccountConfig       sql.NullString `db:"account_config"`        // 池账号配置
		PlanId              string         `db:"plan_id"`               // 计划id
		PlanExecuteId       string         `db:"plan_execute_id"`       // 计划执行id, 作为接口执行表或集合执行表的parent_component_execute_id
		PlanName            string         `db:"plan_name"`             // 计划名称
		TriggerMode         string         `db:"trigger_mode"`          // 触发模式
		PlanPurpose         string         `db:"plan_purpose"`          // 计划用途
		TotalSuite          int64          `db:"total_suite"`           // 计划总集合（或接口）个数
		SuccessSuite        int64          `db:"success_suite"`         // 执行成功集合（或接口）个数
		FailureSuite        int64          `db:"failure_suite"`         // 执行失败集合（或接口）个数
		TotalCase           int64          `db:"total_case"`            // 测试用例总数
		SuccessCase         int64          `db:"success_case"`          // 执行成功的测试用例数
		FailureCase         int64          `db:"failure_case"`          // 执行完成的测试用例数
		Status              sql.NullString `db:"status"`                // 执行状态（结果）
		Content             sql.NullString `db:"content"`               // 执行数据详情
		ServiceCasesContent sql.NullString `db:"service_cases_content"` // 保存每个服务与测试用例关系，用于精准测试
		ExecutedBy          string         `db:"executed_by"`           // 执行人
		ApprovedBy          sql.NullString `db:"approved_by"`           // 审批人
		StartedAt           int64          `db:"started_at"`            // 开始执行的时间(戳)
		EndedAt             sql.NullInt64  `db:"ended_at"`              // 结束执行的时间(戳)
		CostTime            int64          `db:"cost_time"`             // 执行耗时(毫秒)
		CreatedAt           time.Time      `db:"created_at"`            // 创建时间(戳)
		UpdatedAt           time.Time      `db:"updated_at"`            // 更新时间(戳)
		Cleaned             int64          `db:"cleaned"`               // 是否已清理过记录
	}
)

func newPlanExecutionRecordModel(conn sqlx.SqlConn) *defaultPlanExecutionRecordModel {
	return &defaultPlanExecutionRecordModel{
		conn:  conn,
		table: "`plan_execution_record`",
	}
}

func (m *defaultPlanExecutionRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultPlanExecutionRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultPlanExecutionRecordModel) FindOne(ctx context.Context, id int64) (*PlanExecutionRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", planExecutionRecordRows, m.table)
	var resp PlanExecutionRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPlanExecutionRecordModel) FindOneByTaskIdProjectIdExecuteIdPlanExecuteId(ctx context.Context, taskId string, projectId string, executeId string, planExecuteId string) (*PlanExecutionRecord, error) {
	var resp PlanExecutionRecord
	query := fmt.Sprintf("select %s from %s where `task_id` = ? and `project_id` = ? and `execute_id` = ? and `plan_execute_id` = ? and `deleted` = ? limit 1", planExecutionRecordRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, taskId, projectId, executeId, planExecuteId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultPlanExecutionRecordModel) Insert(ctx context.Context, session sqlx.Session, data *PlanExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, planExecutionRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig, data.PlanId, data.PlanExecuteId, data.PlanName, data.TriggerMode, data.PlanPurpose, data.TotalSuite, data.SuccessSuite, data.FailureSuite, data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content, data.ServiceCasesContent, data.ExecutedBy, data.ApprovedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Cleaned)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig, data.PlanId, data.PlanExecuteId, data.PlanName, data.TriggerMode, data.PlanPurpose, data.TotalSuite, data.SuccessSuite, data.FailureSuite, data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content, data.ServiceCasesContent, data.ExecutedBy, data.ApprovedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Cleaned)
}

func (m *defaultPlanExecutionRecordModel) Update(ctx context.Context, session sqlx.Session, newData *PlanExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, planExecutionRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.ProjectId, newData.ExecuteId, newData.ExecuteType, newData.GeneralConfig, newData.AccountConfig, newData.PlanId, newData.PlanExecuteId, newData.PlanName, newData.TriggerMode, newData.PlanPurpose, newData.TotalSuite, newData.SuccessSuite, newData.FailureSuite, newData.TotalCase, newData.SuccessCase, newData.FailureCase, newData.Status, newData.Content, newData.ServiceCasesContent, newData.ExecutedBy, newData.ApprovedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.Cleaned, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.ProjectId, newData.ExecuteId, newData.ExecuteType, newData.GeneralConfig, newData.AccountConfig, newData.PlanId, newData.PlanExecuteId, newData.PlanName, newData.TriggerMode, newData.PlanPurpose, newData.TotalSuite, newData.SuccessSuite, newData.FailureSuite, newData.TotalCase, newData.SuccessCase, newData.FailureCase, newData.Status, newData.Content, newData.ServiceCasesContent, newData.ExecutedBy, newData.ApprovedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.Cleaned, newData.Id)
}

func (m *defaultPlanExecutionRecordModel) tableName() string {
	return m.table
}
