// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	uiCaseExecutionStepContentTableName           = "`ui_case_execution_step_content`"
	uiCaseExecutionStepContentFieldNames          = builder.RawFieldNames(&UiCaseExecutionStepContent{})
	uiCaseExecutionStepContentRows                = strings.Join(uiCaseExecutionStepContentFieldNames, ",")
	uiCaseExecutionStepContentRowsExpectAutoSet   = strings.Join(stringx.Remove(uiCaseExecutionStepContentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	uiCaseExecutionStepContentRowsWithPlaceHolder = strings.Join(stringx.Remove(uiCaseExecutionStepContentFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	uiCaseExecutionStepContentModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *UiCaseExecutionStepContent) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UiCaseExecutionStepContent, error)
		FindOneByStepIdIndex(ctx context.Context, stepId string, index int64) (*UiCaseExecutionStepContent, error)
		Update(ctx context.Context, session sqlx.Session, data *UiCaseExecutionStepContent) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultUiCaseExecutionStepContentModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UiCaseExecutionStepContent struct {
		Id        int64          `db:"id"`         // 自增ID
		TaskId    string         `db:"task_id"`    // 任务ID
		StepId    string         `db:"step_id"`    // 步骤ID
		Content   sql.NullString `db:"content"`    // 步骤分片内容
		Index     int64          `db:"index"`      // 步骤分片内容索引
		Deleted   int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt time.Time      `db:"created_at"` // 创建时间
		UpdatedAt time.Time      `db:"updated_at"` // 更新时间
		DeletedAt sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newUiCaseExecutionStepContentModel(conn sqlx.SqlConn) *defaultUiCaseExecutionStepContentModel {
	return &defaultUiCaseExecutionStepContentModel{
		conn:  conn,
		table: "`ui_case_execution_step_content`",
	}
}

func (m *defaultUiCaseExecutionStepContentModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultUiCaseExecutionStepContentModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultUiCaseExecutionStepContentModel) FindOne(ctx context.Context, id int64) (*UiCaseExecutionStepContent, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", uiCaseExecutionStepContentRows, m.table)
	var resp UiCaseExecutionStepContent
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiCaseExecutionStepContentModel) FindOneByStepIdIndex(ctx context.Context, stepId string, index int64) (*UiCaseExecutionStepContent, error) {
	var resp UiCaseExecutionStepContent
	query := fmt.Sprintf("select %s from %s where `step_id` = ? and `index` = ? and `deleted` = ? limit 1", uiCaseExecutionStepContentRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, stepId, index, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiCaseExecutionStepContentModel) Insert(ctx context.Context, session sqlx.Session, data *UiCaseExecutionStepContent) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?)", m.table, uiCaseExecutionStepContentRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.StepId, data.Content, data.Index, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.StepId, data.Content, data.Index, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
}

func (m *defaultUiCaseExecutionStepContentModel) Update(ctx context.Context, session sqlx.Session, newData *UiCaseExecutionStepContent) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiCaseExecutionStepContentRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.StepId, newData.Content, newData.Index, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.StepId, newData.Content, newData.Index, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
}

func (m *defaultUiCaseExecutionStepContentModel) tableName() string {
	return m.table
}
