// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	interfaceExecutionRecordTableName           = "`interface_execution_record`"
	interfaceExecutionRecordFieldNames          = builder.RawFieldNames(&InterfaceExecutionRecord{})
	interfaceExecutionRecordRows                = strings.Join(interfaceExecutionRecordFieldNames, ",")
	interfaceExecutionRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(interfaceExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	interfaceExecutionRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(interfaceExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	interfaceExecutionRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *InterfaceExecutionRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*InterfaceExecutionRecord, error)
		FindOneByTaskIdProjectIdExecuteIdInterfaceExecuteId(ctx context.Context, taskId string, projectId string, executeId string, interfaceExecuteId string) (*InterfaceExecutionRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *InterfaceExecutionRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultInterfaceExecutionRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	InterfaceExecutionRecord struct {
		Id                 int64          `db:"id"`                   // 自增id
		TaskId             string         `db:"task_id"`              // 任务id
		ProjectId          string         `db:"project_id"`           // 项目id
		ExecuteId          string         `db:"execute_id"`           // 执行id
		ExecuteType        string         `db:"execute_type"`         // 执行类型
		GeneralConfig      sql.NullString `db:"general_config"`       // 通用配置
		AccountConfig      sql.NullString `db:"account_config"`       // 池账号配置
		InterfaceId        string         `db:"interface_id"`         // 接口id
		InterfaceExecuteId string         `db:"interface_execute_id"` // 接口执行id, 作为用例执行表的parent_component_execute_id
		InterfaceName      string         `db:"interface_name"`       // 接口名称
		PlanExecuteId      sql.NullString `db:"plan_execute_id"`      // 计划执行id, 关联表plan_execution_record的plan_execute_id
		TotalCase          int64          `db:"total_case"`           // 接口总用例个数
		SuccessCase        int64          `db:"success_case"`         // 执行成功用例个数
		FailureCase        int64          `db:"failure_case"`         // 执行失败用例个数
		Status             sql.NullString `db:"status"`               // 执行状态（结果）
		Content            sql.NullString `db:"content"`              // 执行数据详情
		ExecutedBy         string         `db:"executed_by"`          // 执行人
		StartedAt          int64          `db:"started_at"`           // 开始执行的时间(戳)
		EndedAt            sql.NullInt64  `db:"ended_at"`             // 结束执行的时间(戳)
		CostTime           int64          `db:"cost_time"`            // 执行耗时(毫秒)
		Callback           sql.NullString `db:"callback"`             // callback日志
		CreatedAt          time.Time      `db:"created_at"`           // 创建时间(戳)
		UpdatedAt          time.Time      `db:"updated_at"`           // 更新时间(戳)
		Cleaned            int64          `db:"cleaned"`              // 是否已清理过记录
	}
)

func newInterfaceExecutionRecordModel(conn sqlx.SqlConn) *defaultInterfaceExecutionRecordModel {
	return &defaultInterfaceExecutionRecordModel{
		conn:  conn,
		table: "`interface_execution_record`",
	}
}

func (m *defaultInterfaceExecutionRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultInterfaceExecutionRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultInterfaceExecutionRecordModel) FindOne(ctx context.Context, id int64) (*InterfaceExecutionRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", interfaceExecutionRecordRows, m.table)
	var resp InterfaceExecutionRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceExecutionRecordModel) FindOneByTaskIdProjectIdExecuteIdInterfaceExecuteId(ctx context.Context, taskId string, projectId string, executeId string, interfaceExecuteId string) (*InterfaceExecutionRecord, error) {
	var resp InterfaceExecutionRecord
	query := fmt.Sprintf("select %s from %s where `task_id` = ? and `project_id` = ? and `execute_id` = ? and `interface_execute_id` = ? and `deleted` = ? limit 1", interfaceExecutionRecordRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, taskId, projectId, executeId, interfaceExecuteId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultInterfaceExecutionRecordModel) Insert(ctx context.Context, session sqlx.Session, data *InterfaceExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, interfaceExecutionRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig, data.InterfaceId, data.InterfaceExecuteId, data.InterfaceName, data.PlanExecuteId, data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig, data.InterfaceId, data.InterfaceExecuteId, data.InterfaceName, data.PlanExecuteId, data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned)
}

func (m *defaultInterfaceExecutionRecordModel) Update(ctx context.Context, session sqlx.Session, newData *InterfaceExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, interfaceExecutionRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.ProjectId, newData.ExecuteId, newData.ExecuteType, newData.GeneralConfig, newData.AccountConfig, newData.InterfaceId, newData.InterfaceExecuteId, newData.InterfaceName, newData.PlanExecuteId, newData.TotalCase, newData.SuccessCase, newData.FailureCase, newData.Status, newData.Content, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.Callback, newData.Cleaned, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.ProjectId, newData.ExecuteId, newData.ExecuteType, newData.GeneralConfig, newData.AccountConfig, newData.InterfaceId, newData.InterfaceExecuteId, newData.InterfaceName, newData.PlanExecuteId, newData.TotalCase, newData.SuccessCase, newData.FailureCase, newData.Status, newData.Content, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.Callback, newData.Cleaned, newData.Id)
}

func (m *defaultInterfaceExecutionRecordModel) tableName() string {
	return m.table
}
