// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	uiDevicePerfDataTableName           = "`ui_device_perf_data`"
	uiDevicePerfDataFieldNames          = builder.RawFieldNames(&UiDevicePerfData{})
	uiDevicePerfDataRows                = strings.Join(uiDevicePerfDataFieldNames, ",")
	uiDevicePerfDataRowsExpectAutoSet   = strings.Join(stringx.Remove(uiDevicePerfDataFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	uiDevicePerfDataRowsWithPlaceHolder = strings.Join(stringx.Remove(uiDevicePerfDataFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	uiDevicePerfDataModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *UiDevicePerfData) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UiDevicePerfData, error)
		Update(ctx context.Context, session sqlx.Session, data *UiDevicePerfData) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultUiDevicePerfDataModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UiDevicePerfData struct {
		Id        int64          `db:"id"`         // 自增ID
		TaskId    string         `db:"task_id"`    // 任务ID
		ExecuteId string         `db:"execute_id"` // 执行ID
		ProjectId string         `db:"project_id"` // 项目ID
		Udid      string         `db:"udid"`       // 设备编号
		DataType  string         `db:"data_type"`  // 数据类型
		Interval  int64          `db:"interval"`   // 采集间隔，单位毫秒
		Series    string         `db:"series"`     // 指标名称
		Unit      string         `db:"unit"`       // 单位
		X         string         `db:"x"`          // X轴数据
		Y         string         `db:"y"`          // Y轴数据
		Deleted   int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt time.Time      `db:"created_at"` // 创建时间
		UpdatedAt time.Time      `db:"updated_at"` // 更新时间
		DeletedAt sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newUiDevicePerfDataModel(conn sqlx.SqlConn) *defaultUiDevicePerfDataModel {
	return &defaultUiDevicePerfDataModel{
		conn:  conn,
		table: "`ui_device_perf_data`",
	}
}

func (m *defaultUiDevicePerfDataModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultUiDevicePerfDataModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultUiDevicePerfDataModel) FindOne(ctx context.Context, id int64) (*UiDevicePerfData, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", uiDevicePerfDataRows, m.table)
	var resp UiDevicePerfData
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiDevicePerfDataModel) Insert(ctx context.Context, session sqlx.Session, data *UiDevicePerfData) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, uiDevicePerfDataRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.Udid, data.DataType, data.Interval, data.Series, data.Unit, data.X, data.Y, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.Udid, data.DataType, data.Interval, data.Series, data.Unit, data.X, data.Y, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
}

func (m *defaultUiDevicePerfDataModel) Update(ctx context.Context, session sqlx.Session, data *UiDevicePerfData) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiDevicePerfDataRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.Udid, data.DataType, data.Interval, data.Series, data.Unit, data.X, data.Y, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.Udid, data.DataType, data.Interval, data.Series, data.Unit, data.X, data.Y, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
}

func (m *defaultUiDevicePerfDataModel) tableName() string {
	return m.table
}
