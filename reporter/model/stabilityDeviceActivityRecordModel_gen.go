// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	stabilityDeviceActivityRecordTableName           = "`stability_device_activity_record`"
	stabilityDeviceActivityRecordFieldNames          = builder.RawFieldNames(&StabilityDeviceActivityRecord{})
	stabilityDeviceActivityRecordRows                = strings.Join(stabilityDeviceActivityRecordFieldNames, ",")
	stabilityDeviceActivityRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(stabilityDeviceActivityRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	stabilityDeviceActivityRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(stabilityDeviceActivityRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	stabilityDeviceActivityRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *StabilityDeviceActivityRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*StabilityDeviceActivityRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *StabilityDeviceActivityRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultStabilityDeviceActivityRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	StabilityDeviceActivityRecord struct {
		Id        int64          `db:"id"`         // 自增ID
		TaskId    string         `db:"task_id"`    // 任务ID
		ExecuteId string         `db:"execute_id"` // 执行ID
		ProjectId string         `db:"project_id"` // 项目ID
		Udid      string         `db:"udid"`       // 设备编号
		Name      string         `db:"name"`       // activity名称
		Covered   int64          `db:"covered"`    // 是否被覆盖（未覆盖，已覆盖）
		Deleted   int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt time.Time      `db:"created_at"` // 创建时间
		UpdatedAt time.Time      `db:"updated_at"` // 更新时间
		DeletedAt sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newStabilityDeviceActivityRecordModel(conn sqlx.SqlConn) *defaultStabilityDeviceActivityRecordModel {
	return &defaultStabilityDeviceActivityRecordModel{
		conn:  conn,
		table: "`stability_device_activity_record`",
	}
}

func (m *defaultStabilityDeviceActivityRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultStabilityDeviceActivityRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultStabilityDeviceActivityRecordModel) FindOne(ctx context.Context, id int64) (*StabilityDeviceActivityRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", stabilityDeviceActivityRecordRows, m.table)
	var resp StabilityDeviceActivityRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultStabilityDeviceActivityRecordModel) Insert(ctx context.Context, session sqlx.Session, data *StabilityDeviceActivityRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, stabilityDeviceActivityRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.Udid, data.Name, data.Covered, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.Udid, data.Name, data.Covered, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
}

func (m *defaultStabilityDeviceActivityRecordModel) Update(ctx context.Context, session sqlx.Session, data *StabilityDeviceActivityRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, stabilityDeviceActivityRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.Udid, data.Name, data.Covered, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.Udid, data.Name, data.Covered, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
}

func (m *defaultStabilityDeviceActivityRecordModel) tableName() string {
	return m.table
}
