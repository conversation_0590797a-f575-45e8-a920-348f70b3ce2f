// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	stabilityPlanExecutionRecordTableName           = "`stability_plan_execution_record`"
	stabilityPlanExecutionRecordFieldNames          = builder.RawFieldNames(&StabilityPlanExecutionRecord{})
	stabilityPlanExecutionRecordRows                = strings.Join(stabilityPlanExecutionRecordFieldNames, ",")
	stabilityPlanExecutionRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(stabilityPlanExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	stabilityPlanExecutionRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(stabilityPlanExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	stabilityPlanExecutionRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *StabilityPlanExecutionRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*StabilityPlanExecutionRecord, error)
		FindOneByProjectIdTaskIdExecuteId(ctx context.Context, projectId string, taskId string, executeId string) (*StabilityPlanExecutionRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *StabilityPlanExecutionRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultStabilityPlanExecutionRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	StabilityPlanExecutionRecord struct {
		Id             int64          `db:"id"`              // 自增ID
		ProjectId      string         `db:"project_id"`      // 项目ID
		PlanId         string         `db:"plan_id"`         // 计划ID
		PlanName       string         `db:"plan_name"`       // 计划名称
		TriggerMode    string         `db:"trigger_mode"`    // 触发模式
		TaskId         string         `db:"task_id"`         // 任务ID
		ExecuteId      string         `db:"execute_id"`      // 执行ID
		Status         sql.NullString `db:"status"`          // 执行状态（结果）
		CostTime       int64          `db:"cost_time"`       // 执行耗时（单位为毫秒）
		TargetDuration int64          `db:"target_duration"` // 目标运行时长（单位为分钟）
		ExecuteData    sql.NullString `db:"execute_data"`    // 执行数据
		ExecutedBy     string         `db:"executed_by"`     // 执行者的用户ID
		SuccessDevice  int64          `db:"success_device"`  // 执行成功的测试设备数
		FailureDevice  int64          `db:"failure_device"`  // 执行失败的测试设备数
		TotalDevice    int64          `db:"total_device"`    // 测试设备总数
		StartedAt      sql.NullTime   `db:"started_at"`      // 开始时间
		EndedAt        sql.NullTime   `db:"ended_at"`        // 结束时间
		ErrMsg         sql.NullString `db:"err_msg"`         // 计划执行错误信息
		Cleaned        int64          `db:"cleaned"`         // 清理标识（未清理、已清理）
		Deleted        int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		CreatedBy      string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy      string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy      sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt      time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt      time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt      sql.NullTime   `db:"deleted_at"`      // 删除时间
	}
)

func newStabilityPlanExecutionRecordModel(conn sqlx.SqlConn) *defaultStabilityPlanExecutionRecordModel {
	return &defaultStabilityPlanExecutionRecordModel{
		conn:  conn,
		table: "`stability_plan_execution_record`",
	}
}

func (m *defaultStabilityPlanExecutionRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultStabilityPlanExecutionRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultStabilityPlanExecutionRecordModel) FindOne(ctx context.Context, id int64) (*StabilityPlanExecutionRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", stabilityPlanExecutionRecordRows, m.table)
	var resp StabilityPlanExecutionRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultStabilityPlanExecutionRecordModel) FindOneByProjectIdTaskIdExecuteId(ctx context.Context, projectId string, taskId string, executeId string) (*StabilityPlanExecutionRecord, error) {
	var resp StabilityPlanExecutionRecord
	query := fmt.Sprintf("select %s from %s where `project_id` = ? and `task_id` = ? and `execute_id` = ? and `deleted` = ? limit 1", stabilityPlanExecutionRecordRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, projectId, taskId, executeId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultStabilityPlanExecutionRecordModel) Insert(ctx context.Context, session sqlx.Session, data *StabilityPlanExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, stabilityPlanExecutionRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.ExecuteId, data.Status, data.CostTime, data.TargetDuration, data.ExecuteData, data.ExecutedBy, data.SuccessDevice, data.FailureDevice, data.TotalDevice, data.StartedAt, data.EndedAt, data.ErrMsg, data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.ExecuteId, data.Status, data.CostTime, data.TargetDuration, data.ExecuteData, data.ExecutedBy, data.SuccessDevice, data.FailureDevice, data.TotalDevice, data.StartedAt, data.EndedAt, data.ErrMsg, data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
}

func (m *defaultStabilityPlanExecutionRecordModel) Update(ctx context.Context, session sqlx.Session, newData *StabilityPlanExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, stabilityPlanExecutionRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.ProjectId, newData.PlanId, newData.PlanName, newData.TriggerMode, newData.TaskId, newData.ExecuteId, newData.Status, newData.CostTime, newData.TargetDuration, newData.ExecuteData, newData.ExecutedBy, newData.SuccessDevice, newData.FailureDevice, newData.TotalDevice, newData.StartedAt, newData.EndedAt, newData.ErrMsg, newData.Cleaned, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.ProjectId, newData.PlanId, newData.PlanName, newData.TriggerMode, newData.TaskId, newData.ExecuteId, newData.Status, newData.CostTime, newData.TargetDuration, newData.ExecuteData, newData.ExecutedBy, newData.SuccessDevice, newData.FailureDevice, newData.TotalDevice, newData.StartedAt, newData.EndedAt, newData.ErrMsg, newData.Cleaned, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
}

func (m *defaultStabilityPlanExecutionRecordModel) tableName() string {
	return m.table
}
