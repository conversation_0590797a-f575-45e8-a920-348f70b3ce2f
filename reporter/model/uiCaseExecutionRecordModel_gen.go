// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	uiCaseExecutionRecordTableName           = "`ui_case_execution_record`"
	uiCaseExecutionRecordFieldNames          = builder.RawFieldNames(&UiCaseExecutionRecord{})
	uiCaseExecutionRecordRows                = strings.Join(uiCaseExecutionRecordFieldNames, ",")
	uiCaseExecutionRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(uiCaseExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	uiCaseExecutionRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(uiCaseExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	uiCaseExecutionRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *UiCaseExecutionRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UiCaseExecutionRecord, error)
		FindOneByTaskIdProjectIdExecuteId(ctx context.Context, taskId string, projectId string, executeId string) (*UiCaseExecutionRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *UiCaseExecutionRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultUiCaseExecutionRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UiCaseExecutionRecord struct {
		Id             int64          `db:"id"`               // 自增id
		TaskId         string         `db:"task_id"`          // 任务id
		ProjectId      string         `db:"project_id"`       // 项目id
		ExecuteId      string         `db:"execute_id"`       // 用例执行id
		CaseId         string         `db:"case_id"`          // 用例ID
		CaseName       string         `db:"case_name"`        // 用例名称
		SuiteExecuteId string         `db:"suite_execute_id"` // 所属集合执行id
		Udid           string         `db:"udid"`             // 设备编号
		Status         sql.NullString `db:"status"`           // 执行状态（结果）
		Content        sql.NullString `db:"content"`          // 执行数据详情
		ExecutedBy     string         `db:"executed_by"`      // 执行人
		StartedAt      int64          `db:"started_at"`       // 开始执行的时间(戳)
		EndedAt        sql.NullInt64  `db:"ended_at"`         // 结束执行的时间(戳)
		CostTime       int64          `db:"cost_time"`        // 执行耗时(毫秒)
		Callback       sql.NullString `db:"callback"`         // callback日志
		CreatedAt      time.Time      `db:"created_at"`       // 创建时间(戳)
		UpdatedAt      time.Time      `db:"updated_at"`       // 更新时间(戳)
		Cleaned        int64          `db:"cleaned"`          // 是否已清理过记录
	}
)

func newUiCaseExecutionRecordModel(conn sqlx.SqlConn) *defaultUiCaseExecutionRecordModel {
	return &defaultUiCaseExecutionRecordModel{
		conn:  conn,
		table: "`ui_case_execution_record`",
	}
}

func (m *defaultUiCaseExecutionRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultUiCaseExecutionRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultUiCaseExecutionRecordModel) FindOne(ctx context.Context, id int64) (*UiCaseExecutionRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", uiCaseExecutionRecordRows, m.table)
	var resp UiCaseExecutionRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiCaseExecutionRecordModel) FindOneByTaskIdProjectIdExecuteId(ctx context.Context, taskId string, projectId string, executeId string) (*UiCaseExecutionRecord, error) {
	var resp UiCaseExecutionRecord
	query := fmt.Sprintf("select %s from %s where `task_id` = ? and `project_id` = ? and `execute_id` = ? and `deleted` = ? limit 1", uiCaseExecutionRecordRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, taskId, projectId, executeId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiCaseExecutionRecordModel) Insert(ctx context.Context, session sqlx.Session, data *UiCaseExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, uiCaseExecutionRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ProjectId, data.ExecuteId, data.CaseId, data.CaseName, data.SuiteExecuteId, data.Udid, data.Status, data.Content, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ProjectId, data.ExecuteId, data.CaseId, data.CaseName, data.SuiteExecuteId, data.Udid, data.Status, data.Content, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned)
}

func (m *defaultUiCaseExecutionRecordModel) Update(ctx context.Context, session sqlx.Session, newData *UiCaseExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiCaseExecutionRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.ProjectId, newData.ExecuteId, newData.CaseId, newData.CaseName, newData.SuiteExecuteId, newData.Udid, newData.Status, newData.Content, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.Callback, newData.Cleaned, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.ProjectId, newData.ExecuteId, newData.CaseId, newData.CaseName, newData.SuiteExecuteId, newData.Udid, newData.Status, newData.Content, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.Callback, newData.Cleaned, newData.Id)
}

func (m *defaultUiCaseExecutionRecordModel) tableName() string {
	return m.table
}
