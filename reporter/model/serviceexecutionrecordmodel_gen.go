// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	serviceExecutionRecordTableName           = "`service_execution_record`"
	serviceExecutionRecordFieldNames          = builder.RawFieldNames(&ServiceExecutionRecord{})
	serviceExecutionRecordRows                = strings.Join(serviceExecutionRecordFieldNames, ",")
	serviceExecutionRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(serviceExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	serviceExecutionRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(serviceExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	serviceExecutionRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *ServiceExecutionRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*ServiceExecutionRecord, error)
		FindOneByTaskIdProjectIdExecuteIdServiceExecuteId(ctx context.Context, taskId string, projectId string, executeId string, serviceExecuteId string) (*ServiceExecutionRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *ServiceExecutionRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultServiceExecutionRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	ServiceExecutionRecord struct {
		Id               int64          `db:"id"`                 // 自增id
		TaskId           string         `db:"task_id"`            // 任务id
		ProjectId        string         `db:"project_id"`         // 项目id
		ExecuteId        string         `db:"execute_id"`         // 执行id
		ExecuteType      string         `db:"execute_type"`       // 执行类型
		GeneralConfig    sql.NullString `db:"general_config"`     // 通用配置
		AccountConfig    sql.NullString `db:"account_config"`     // 池账号配置
		ServiceId        string         `db:"service_id"`         // 服务id
		ServiceExecuteId string         `db:"service_execute_id"` // 服务执行id, 作为用例执行表的parent_component_execute_id
		ServiceName      string         `db:"service_name"`       // 服务名称
		PlanExecuteId    sql.NullString `db:"plan_execute_id"`    // 计划执行id, 关联表plan_execution_record的plan_execute_id
		TotalCase        int64          `db:"total_case"`         // 集合总用例个数
		SuccessCase      int64          `db:"success_case"`       // 执行成功用例个数
		FailureCase      int64          `db:"failure_case"`       // 执行失败用例个数
		Status           sql.NullString `db:"status"`             // 执行状态（结果）
		Content          sql.NullString `db:"content"`            // 执行数据详情
		ExecutedBy       string         `db:"executed_by"`        // 执行人
		StartedAt        int64          `db:"started_at"`         // 开始执行的时间(戳)
		EndedAt          sql.NullInt64  `db:"ended_at"`           // 结束执行的时间(戳)
		CostTime         int64          `db:"cost_time"`          // 执行耗时(毫秒)
		Callback         sql.NullString `db:"callback"`           // callback日志
		CreatedAt        time.Time      `db:"created_at"`         // 创建时间(戳)
		UpdatedAt        time.Time      `db:"updated_at"`         // 更新时间(戳)
		Cleaned          int64          `db:"cleaned"`            // 是否已清理过记录
	}
)

func newServiceExecutionRecordModel(conn sqlx.SqlConn) *defaultServiceExecutionRecordModel {
	return &defaultServiceExecutionRecordModel{
		conn:  conn,
		table: "`service_execution_record`",
	}
}

func (m *defaultServiceExecutionRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultServiceExecutionRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultServiceExecutionRecordModel) FindOne(ctx context.Context, id int64) (*ServiceExecutionRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", serviceExecutionRecordRows, m.table)
	var resp ServiceExecutionRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultServiceExecutionRecordModel) FindOneByTaskIdProjectIdExecuteIdServiceExecuteId(ctx context.Context, taskId string, projectId string, executeId string, serviceExecuteId string) (*ServiceExecutionRecord, error) {
	var resp ServiceExecutionRecord
	query := fmt.Sprintf("select %s from %s where `task_id` = ? and `project_id` = ? and `execute_id` = ? and `service_execute_id` = ? and `deleted` = ? limit 1", serviceExecutionRecordRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, taskId, projectId, executeId, serviceExecuteId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultServiceExecutionRecordModel) Insert(ctx context.Context, session sqlx.Session, data *ServiceExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, serviceExecutionRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig, data.ServiceId, data.ServiceExecuteId, data.ServiceName, data.PlanExecuteId, data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ProjectId, data.ExecuteId, data.ExecuteType, data.GeneralConfig, data.AccountConfig, data.ServiceId, data.ServiceExecuteId, data.ServiceName, data.PlanExecuteId, data.TotalCase, data.SuccessCase, data.FailureCase, data.Status, data.Content, data.ExecutedBy, data.StartedAt, data.EndedAt, data.CostTime, data.Callback, data.Cleaned)
}

func (m *defaultServiceExecutionRecordModel) Update(ctx context.Context, session sqlx.Session, newData *ServiceExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, serviceExecutionRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.ProjectId, newData.ExecuteId, newData.ExecuteType, newData.GeneralConfig, newData.AccountConfig, newData.ServiceId, newData.ServiceExecuteId, newData.ServiceName, newData.PlanExecuteId, newData.TotalCase, newData.SuccessCase, newData.FailureCase, newData.Status, newData.Content, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.Callback, newData.Cleaned, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.ProjectId, newData.ExecuteId, newData.ExecuteType, newData.GeneralConfig, newData.AccountConfig, newData.ServiceId, newData.ServiceExecuteId, newData.ServiceName, newData.PlanExecuteId, newData.TotalCase, newData.SuccessCase, newData.FailureCase, newData.Status, newData.Content, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.CostTime, newData.Callback, newData.Cleaned, newData.Id)
}

func (m *defaultServiceExecutionRecordModel) tableName() string {
	return m.table
}
