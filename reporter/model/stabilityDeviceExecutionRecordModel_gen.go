// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	stabilityDeviceExecutionRecordTableName           = "`stability_device_execution_record`"
	stabilityDeviceExecutionRecordFieldNames          = builder.RawFieldNames(&StabilityDeviceExecutionRecord{})
	stabilityDeviceExecutionRecordRows                = strings.Join(stabilityDeviceExecutionRecordFieldNames, ",")
	stabilityDeviceExecutionRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(stabilityDeviceExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	stabilityDeviceExecutionRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(stabilityDeviceExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	stabilityDeviceExecutionRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *StabilityDeviceExecutionRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*StabilityDeviceExecutionRecord, error)
		FindOneByProjectIdTaskIdExecuteId(ctx context.Context, projectId string, taskId string, executeId string) (*StabilityDeviceExecutionRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *StabilityDeviceExecutionRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultStabilityDeviceExecutionRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	StabilityDeviceExecutionRecord struct {
		Id            int64          `db:"id"`              // 自增ID
		TaskId        string         `db:"task_id"`         // 任务ID
		ExecuteId     string         `db:"execute_id"`      // 执行ID
		ProjectId     string         `db:"project_id"`      // 项目ID
		PlanExecuteId string         `db:"plan_execute_id"` // 计划执行ID
		Udid          string         `db:"udid"`            // 设备编号
		Device        sql.NullString `db:"device"`          // 设备执行信息
		Status        sql.NullString `db:"status"`          // 执行状态（结果）
		CostTime      int64          `db:"cost_time"`       // 执行耗时（单位为毫秒）
		CrashCount    int64          `db:"crash_count"`     // crash数量
		AnrCount      int64          `db:"anr_count"`       // anr数量
		Result        sql.NullString `db:"result"`          // 执行结果
		ExecutedBy    string         `db:"executed_by"`     // 执行者的用户ID
		StartedAt     sql.NullTime   `db:"started_at"`      // 开始时间
		EndedAt       sql.NullTime   `db:"ended_at"`        // 结束时间
		ErrMsg        sql.NullString `db:"err_msg"`         // 用例执行错误信息
		Cleaned       int64          `db:"cleaned"`         // 清理标识（未清理、已清理）
		Deleted       int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`      // 删除时间
	}
)

func newStabilityDeviceExecutionRecordModel(conn sqlx.SqlConn) *defaultStabilityDeviceExecutionRecordModel {
	return &defaultStabilityDeviceExecutionRecordModel{
		conn:  conn,
		table: "`stability_device_execution_record`",
	}
}

func (m *defaultStabilityDeviceExecutionRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultStabilityDeviceExecutionRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultStabilityDeviceExecutionRecordModel) FindOne(ctx context.Context, id int64) (*StabilityDeviceExecutionRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", stabilityDeviceExecutionRecordRows, m.table)
	var resp StabilityDeviceExecutionRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultStabilityDeviceExecutionRecordModel) FindOneByProjectIdTaskIdExecuteId(ctx context.Context, projectId string, taskId string, executeId string) (*StabilityDeviceExecutionRecord, error) {
	var resp StabilityDeviceExecutionRecord
	query := fmt.Sprintf("select %s from %s where `project_id` = ? and `task_id` = ? and `execute_id` = ? and `deleted` = ? limit 1", stabilityDeviceExecutionRecordRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, projectId, taskId, executeId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultStabilityDeviceExecutionRecordModel) Insert(ctx context.Context, session sqlx.Session, data *StabilityDeviceExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, stabilityDeviceExecutionRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.PlanExecuteId, data.Udid, data.Device, data.Status, data.CostTime, data.CrashCount, data.AnrCount, data.Result, data.ExecutedBy, data.StartedAt, data.EndedAt, data.ErrMsg, data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.PlanExecuteId, data.Udid, data.Device, data.Status, data.CostTime, data.CrashCount, data.AnrCount, data.Result, data.ExecutedBy, data.StartedAt, data.EndedAt, data.ErrMsg, data.Cleaned, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
}

func (m *defaultStabilityDeviceExecutionRecordModel) Update(ctx context.Context, session sqlx.Session, newData *StabilityDeviceExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, stabilityDeviceExecutionRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.ProjectId, newData.PlanExecuteId, newData.Udid, newData.Device, newData.Status, newData.CostTime, newData.CrashCount, newData.AnrCount, newData.Result, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.ErrMsg, newData.Cleaned, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.ProjectId, newData.PlanExecuteId, newData.Udid, newData.Device, newData.Status, newData.CostTime, newData.CrashCount, newData.AnrCount, newData.Result, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.ErrMsg, newData.Cleaned, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
}

func (m *defaultStabilityDeviceExecutionRecordModel) tableName() string {
	return m.table
}
