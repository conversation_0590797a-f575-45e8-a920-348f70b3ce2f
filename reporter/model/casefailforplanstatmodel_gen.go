// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	caseFailForPlanStatTableName           = "`case_fail_for_plan_stat`"
	caseFailForPlanStatFieldNames          = builder.RawFieldNames(&CaseFailForPlanStat{})
	caseFailForPlanStatRows                = strings.Join(caseFailForPlanStatFieldNames, ",")
	caseFailForPlanStatRowsExpectAutoSet   = strings.Join(stringx.Remove(caseFailForPlanStatFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	caseFailForPlanStatRowsWithPlaceHolder = strings.Join(stringx.Remove(caseFailForPlanStatFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	caseFailForPlanStatModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *CaseFailForPlanStat) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*CaseFailForPlanStat, error)
		Update(ctx context.Context, session sqlx.Session, data *CaseFailForPlanStat) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultCaseFailForPlanStatModel struct {
		conn  sqlx.SqlConn
		table string
	}

	CaseFailForPlanStat struct {
		Id            int64          `db:"id"`              // 自增ID
		ProjectId     string         `db:"project_id"`      // 项目ID
		TaskId        string         `db:"task_id"`         // 任务ID
		PlanId        string         `db:"plan_id"`         // 计划ID
		CaseId        string         `db:"case_id"`         // 用例ID
		ExecuteId     string         `db:"execute_id"`      // 执行ID
		CaseType      string         `db:"case_type"`       // 用例类型[API_CASE，INTERFACE_CASE]
		FailReasonUrl sql.NullString `db:"fail_reason_url"` // 失败原因-path，上传文件服务器后的地址
		Deleted       int64          `db:"deleted"`         // 逻辑删除标识（未删除、已删除）
		CreatedBy     string         `db:"created_by"`      // 创建者的用户ID
		UpdatedBy     string         `db:"updated_by"`      // 最近一次更新者的用户ID
		DeletedBy     sql.NullString `db:"deleted_by"`      // 删除者的用户ID
		CreatedAt     time.Time      `db:"created_at"`      // 创建时间
		UpdatedAt     time.Time      `db:"updated_at"`      // 更新时间
		DeletedAt     sql.NullTime   `db:"deleted_at"`      // 删除时间
	}
)

func newCaseFailForPlanStatModel(conn sqlx.SqlConn) *defaultCaseFailForPlanStatModel {
	return &defaultCaseFailForPlanStatModel{
		conn:  conn,
		table: "`case_fail_for_plan_stat`",
	}
}

func (m *defaultCaseFailForPlanStatModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultCaseFailForPlanStatModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultCaseFailForPlanStatModel) FindOne(ctx context.Context, id int64) (*CaseFailForPlanStat, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", caseFailForPlanStatRows, m.table)
	var resp CaseFailForPlanStat
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultCaseFailForPlanStatModel) Insert(ctx context.Context, session sqlx.Session, data *CaseFailForPlanStat) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, caseFailForPlanStatRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.ProjectId, data.TaskId, data.PlanId, data.CaseId, data.ExecuteId, data.CaseType, data.FailReasonUrl, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.ProjectId, data.TaskId, data.PlanId, data.CaseId, data.ExecuteId, data.CaseType, data.FailReasonUrl, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
}

func (m *defaultCaseFailForPlanStatModel) Update(ctx context.Context, session sqlx.Session, data *CaseFailForPlanStat) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, caseFailForPlanStatRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, data.ProjectId, data.TaskId, data.PlanId, data.CaseId, data.ExecuteId, data.CaseType, data.FailReasonUrl, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
	}
	return m.conn.ExecCtx(ctx, query, data.ProjectId, data.TaskId, data.PlanId, data.CaseId, data.ExecuteId, data.CaseType, data.FailReasonUrl, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy, data.Id)
}

func (m *defaultCaseFailForPlanStatModel) tableName() string {
	return m.table
}
