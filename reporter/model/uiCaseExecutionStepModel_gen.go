// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	uiCaseExecutionStepTableName           = "`ui_case_execution_step`"
	uiCaseExecutionStepFieldNames          = builder.RawFieldNames(&UiCaseExecutionStep{})
	uiCaseExecutionStepRows                = strings.Join(uiCaseExecutionStepFieldNames, ",")
	uiCaseExecutionStepRowsExpectAutoSet   = strings.Join(stringx.Remove(uiCaseExecutionStepFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	uiCaseExecutionStepRowsWithPlaceHolder = strings.Join(stringx.Remove(uiCaseExecutionStepFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	uiCaseExecutionStepModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *UiCaseExecutionStep) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UiCaseExecutionStep, error)
		FindOneByStepId(ctx context.Context, stepId string) (*UiCaseExecutionStep, error)
		Update(ctx context.Context, session sqlx.Session, data *UiCaseExecutionStep) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultUiCaseExecutionStepModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UiCaseExecutionStep struct {
		Id        int64          `db:"id"`         // 自增ID
		TaskId    string         `db:"task_id"`    // 任务ID
		ExecuteId string         `db:"execute_id"` // 用例执行ID
		ProjectId string         `db:"project_id"` // 项目ID
		CaseId    string         `db:"case_id"`    // 用例ID
		StepId    string         `db:"step_id"`    // 步骤ID
		Stage     int64          `db:"stage"`      // 阶段（前置步骤、测试步骤、后置步骤）
		Index     int64          `db:"index"`      // 步骤索引
		Name      string         `db:"name"`       // 步骤名称
		Status    string         `db:"status"`     // 状态（成功、失败）
		StartedAt time.Time      `db:"started_at"` // 开始时间
		EndedAt   time.Time      `db:"ended_at"`   // 结束时间
		Deleted   int64          `db:"deleted"`    // 逻辑删除标识（未删除、已删除）
		CreatedBy string         `db:"created_by"` // 创建者的用户ID
		UpdatedBy string         `db:"updated_by"` // 最近一次更新者的用户ID
		DeletedBy sql.NullString `db:"deleted_by"` // 删除者的用户ID
		CreatedAt time.Time      `db:"created_at"` // 创建时间
		UpdatedAt time.Time      `db:"updated_at"` // 更新时间
		DeletedAt sql.NullTime   `db:"deleted_at"` // 删除时间
	}
)

func newUiCaseExecutionStepModel(conn sqlx.SqlConn) *defaultUiCaseExecutionStepModel {
	return &defaultUiCaseExecutionStepModel{
		conn:  conn,
		table: "`ui_case_execution_step`",
	}
}

func (m *defaultUiCaseExecutionStepModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultUiCaseExecutionStepModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultUiCaseExecutionStepModel) FindOne(ctx context.Context, id int64) (*UiCaseExecutionStep, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", uiCaseExecutionStepRows, m.table)
	var resp UiCaseExecutionStep
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiCaseExecutionStepModel) FindOneByStepId(ctx context.Context, stepId string) (*UiCaseExecutionStep, error) {
	var resp UiCaseExecutionStep
	query := fmt.Sprintf("select %s from %s where `step_id` = ? and `deleted` = ? limit 1", uiCaseExecutionStepRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, stepId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiCaseExecutionStepModel) Insert(ctx context.Context, session sqlx.Session, data *UiCaseExecutionStep) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, uiCaseExecutionStepRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.CaseId, data.StepId, data.Stage, data.Index, data.Name, data.Status, data.StartedAt, data.EndedAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
	}
	return m.conn.ExecCtx(ctx, query, data.TaskId, data.ExecuteId, data.ProjectId, data.CaseId, data.StepId, data.Stage, data.Index, data.Name, data.Status, data.StartedAt, data.EndedAt, data.Deleted, data.CreatedBy, data.UpdatedBy, data.DeletedBy)
}

func (m *defaultUiCaseExecutionStepModel) Update(ctx context.Context, session sqlx.Session, newData *UiCaseExecutionStep) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiCaseExecutionStepRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.ProjectId, newData.CaseId, newData.StepId, newData.Stage, newData.Index, newData.Name, newData.Status, newData.StartedAt, newData.EndedAt, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.TaskId, newData.ExecuteId, newData.ProjectId, newData.CaseId, newData.StepId, newData.Stage, newData.Index, newData.Name, newData.Status, newData.StartedAt, newData.EndedAt, newData.Deleted, newData.CreatedBy, newData.UpdatedBy, newData.DeletedBy, newData.Id)
}

func (m *defaultUiCaseExecutionStepModel) tableName() string {
	return m.table
}
