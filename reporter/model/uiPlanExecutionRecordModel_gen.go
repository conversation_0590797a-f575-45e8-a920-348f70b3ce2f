// Code generated by goctl. DO NOT EDIT.
// versions:
//  goctl version: 1.8.3

package model

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/builder"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/core/stringx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
)

var (
	uiPlanExecutionRecordTableName           = "`ui_plan_execution_record`"
	uiPlanExecutionRecordFieldNames          = builder.RawFieldNames(&UiPlanExecutionRecord{})
	uiPlanExecutionRecordRows                = strings.Join(uiPlanExecutionRecordFieldNames, ",")
	uiPlanExecutionRecordRowsExpectAutoSet   = strings.Join(stringx.Remove(uiPlanExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), ",")
	uiPlanExecutionRecordRowsWithPlaceHolder = strings.Join(stringx.Remove(uiPlanExecutionRecordFieldNames, "`id`", "`created_at`", "`deleted_at`", "`updated_at`"), "=?,") + "=?"
)

type (
	uiPlanExecutionRecordModel interface {
		Insert(ctx context.Context, session sqlx.Session, data *UiPlanExecutionRecord) (sql.Result, error)
		FindOne(ctx context.Context, id int64) (*UiPlanExecutionRecord, error)
		FindOneByTaskIdProjectIdExecuteId(ctx context.Context, taskId string, projectId string, executeId string) (*UiPlanExecutionRecord, error)
		Update(ctx context.Context, session sqlx.Session, data *UiPlanExecutionRecord) (sql.Result, error)
		Delete(ctx context.Context, session sqlx.Session, id int64) error
		LogicDelete(ctx context.Context, session sqlx.Session, id int64) error
	}

	defaultUiPlanExecutionRecordModel struct {
		conn  sqlx.SqlConn
		table string
	}

	UiPlanExecutionRecord struct {
		Id             int64          `db:"id"`              // 自增id
		ProjectId      string         `db:"project_id"`      // 项目id
		PlanId         string         `db:"plan_id"`         // 计划id
		PlanName       string         `db:"plan_name"`       // 计划名称
		TriggerMode    string         `db:"trigger_mode"`    // 触发模式
		TaskId         string         `db:"task_id"`         // 任务id
		ExecuteId      string         `db:"execute_id"`      // 执行id
		Status         sql.NullString `db:"status"`          // 执行状态（结果）
		CostTime       int64          `db:"cost_time"`       // 执行耗时(豪秒)
		ExecutedBy     string         `db:"executed_by"`     // 执行人id
		StartedAt      int64          `db:"started_at"`      // 开始执行的时间(戳)
		EndedAt        sql.NullInt64  `db:"ended_at"`        // 结束执行的时间(戳)
		TotalSuite     int64          `db:"total_suite"`     // 测试集合总数
		FinishedSuite  int64          `db:"finished_suite"`  // 执行完的测试集合数
		SuccessSuite   int64          `db:"success_suite"`   // 执行成功的测试集合数
		TotalCase      int64          `db:"total_case"`      // 测试用例总数
		FinishedCase   int64          `db:"finished_case"`   // 执行完成的测试用例数
		SuccessCase    int64          `db:"success_case"`    // 执行成功的测试用例数
		Content        sql.NullString `db:"content"`         // 执行数据详情
		Finished       int64          `db:"finished"`        // 计划是否执行完，0表示未执行完，1表示执行完
		ExecuteData    sql.NullString `db:"execute_data"`    // 执行数据
		CreatedAt      time.Time      `db:"created_at"`      // 创建时间(戳)
		UpdatedAt      time.Time      `db:"updated_at"`      // 更新时间(戳)
		Cleaned        int64          `db:"cleaned"`         // 是否已清理过记录
		PriorityType   int64          `db:"priority_type"`   // 优先级策略
		ExecuteStatus  int64          `db:"execute_status"`  // 0排队中,1执行中,2已完成,3已停止
		ExecutedResult int64          `db:"executed_result"` // 执行结果(1成功,2失败,3异常)
		WaitTime       int64          `db:"wait_time"`       // 排队耗时
		UpdateAt       sql.NullInt64  `db:"update_at"`       // 更新时间
	}
)

func newUiPlanExecutionRecordModel(conn sqlx.SqlConn) *defaultUiPlanExecutionRecordModel {
	return &defaultUiPlanExecutionRecordModel{
		conn:  conn,
		table: "`ui_plan_execution_record`",
	}
}

func (m *defaultUiPlanExecutionRecordModel) Delete(ctx context.Context, session sqlx.Session, id int64) error {
	query := fmt.Sprintf("delete from %s where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, id)
	return err
}

func (m *defaultUiPlanExecutionRecordModel) LogicDelete(ctx context.Context, session sqlx.Session, id int64) error {

	query := fmt.Sprintf("update %s set `deleted` = ?, `deleted_at` = ? where `id` = ?", m.table)
	if session != nil {
		_, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, id)
	return err
}

func (m *defaultUiPlanExecutionRecordModel) FindOne(ctx context.Context, id int64) (*UiPlanExecutionRecord, error) {
	query := fmt.Sprintf("select %s from %s where `id` = ? and `deleted` = ? limit 1", uiPlanExecutionRecordRows, m.table)
	var resp UiPlanExecutionRecord
	err := m.conn.QueryRowCtx(ctx, &resp, query, id, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiPlanExecutionRecordModel) FindOneByTaskIdProjectIdExecuteId(ctx context.Context, taskId string, projectId string, executeId string) (*UiPlanExecutionRecord, error) {
	var resp UiPlanExecutionRecord
	query := fmt.Sprintf("select %s from %s where `task_id` = ? and `project_id` = ? and `execute_id` = ? and `deleted` = ? limit 1", uiPlanExecutionRecordRows, m.table)
	err := m.conn.QueryRowCtx(ctx, &resp, query, taskId, projectId, executeId, constants.NotDeleted)
	switch err {
	case nil:
		return &resp, nil
	case sqlx.ErrNotFound:
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *defaultUiPlanExecutionRecordModel) Insert(ctx context.Context, session sqlx.Session, data *UiPlanExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("insert into %s (%s) values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)", m.table, uiPlanExecutionRecordRowsExpectAutoSet)
	if session != nil {
		return session.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.ExecuteId, data.Status, data.CostTime, data.ExecutedBy, data.StartedAt, data.EndedAt, data.TotalSuite, data.FinishedSuite, data.SuccessSuite, data.TotalCase, data.FinishedCase, data.SuccessCase, data.Content, data.Finished, data.ExecuteData, data.Cleaned, data.PriorityType, data.ExecuteStatus, data.ExecutedResult, data.WaitTime, data.UpdateAt)
	}
	return m.conn.ExecCtx(ctx, query, data.ProjectId, data.PlanId, data.PlanName, data.TriggerMode, data.TaskId, data.ExecuteId, data.Status, data.CostTime, data.ExecutedBy, data.StartedAt, data.EndedAt, data.TotalSuite, data.FinishedSuite, data.SuccessSuite, data.TotalCase, data.FinishedCase, data.SuccessCase, data.Content, data.Finished, data.ExecuteData, data.Cleaned, data.PriorityType, data.ExecuteStatus, data.ExecutedResult, data.WaitTime, data.UpdateAt)
}

func (m *defaultUiPlanExecutionRecordModel) Update(ctx context.Context, session sqlx.Session, newData *UiPlanExecutionRecord) (sql.Result, error) {
	query := fmt.Sprintf("update %s set %s where `id` = ?", m.table, uiPlanExecutionRecordRowsWithPlaceHolder)
	if session != nil {
		return session.ExecCtx(ctx, query, newData.ProjectId, newData.PlanId, newData.PlanName, newData.TriggerMode, newData.TaskId, newData.ExecuteId, newData.Status, newData.CostTime, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.TotalSuite, newData.FinishedSuite, newData.SuccessSuite, newData.TotalCase, newData.FinishedCase, newData.SuccessCase, newData.Content, newData.Finished, newData.ExecuteData, newData.Cleaned, newData.PriorityType, newData.ExecuteStatus, newData.ExecutedResult, newData.WaitTime, newData.UpdateAt, newData.Id)
	}
	return m.conn.ExecCtx(ctx, query, newData.ProjectId, newData.PlanId, newData.PlanName, newData.TriggerMode, newData.TaskId, newData.ExecuteId, newData.Status, newData.CostTime, newData.ExecutedBy, newData.StartedAt, newData.EndedAt, newData.TotalSuite, newData.FinishedSuite, newData.SuccessSuite, newData.TotalCase, newData.FinishedCase, newData.SuccessCase, newData.Content, newData.Finished, newData.ExecuteData, newData.Cleaned, newData.PriorityType, newData.ExecuteStatus, newData.ExecutedResult, newData.WaitTime, newData.UpdateAt, newData.Id)
}

func (m *defaultUiPlanExecutionRecordModel) tableName() string {
	return m.table
}
