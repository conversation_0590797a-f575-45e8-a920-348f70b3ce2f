project=$(basename $(dirname $(realpath $0)))
base_path=$(dirname $(dirname $(realpath $0)))
echo "Project: ${project}\nBase Path: ${base_path}\n"

version=""
module=""

function goctl_version() {
  type goctl > /dev/null 2>&1
  if [ $? -ne 0 ]; then
    echo "goctl tool not found, please install first!"
    echo "cmd: go install github.com/zeromicro/go-zero/tools/goctl@latest"
    exit 1
  fi
  version=$(goctl --version | awk '{print $3}')
  echo "goctl version: ${version}"
}

function current_module() {
  module=$(grep -E "^module" "${base_path}/go.mod" | awk '{print $2}')
  if [ "${module}" == "" ]; then
    echo "current module not found, please check the file[${base_path}/go.mod]"
    exit 1
  fi
  echo "current module: ${module}"
}

function api() {
  goctl api go --api="${base_path}/${project}/api/desc/${project}.api" --dir="${base_path}/${project}/api" --home="${base_path}/goctltemplate/v${version}" --style="goZero"
  echo "NOTE: You need to manually modify the contents of the '${base_path}/${project}/api/internal/types/types.go' file if necessary"
}

function rpc() {
  input_path="${base_path}/protos/${project}"
  output_path="${base_path}/${project}/rpc"
  module_prefix="${module}/${project}/rpc/pb"

  third_party_proto_paths=$(go list -m -f '{{.Dir}}' 'github.com/envoyproxy/protoc-gen-validate' | xargs -I {} echo -n "--proto_path='{}' ")
  private_proto_paths=$(go list -m -f '{{.Dir}}' 'gitlab.ttyuyin.com/TestDevelopment/qet-backend-common' 'gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware' ${module} | xargs -I {} echo -n "--proto_path='{}/protos' ")

  goctl_cmd="goctl rpc protoc ${third_party_proto_paths} ${private_proto_paths} --go_out=${output_path}/pb --go-grpc_out=${output_path}/pb --zrpc_out=${output_path} --go_opt=module=${module_prefix} --go-grpc_opt=module=${module_prefix} ${input_path}/${project}.proto --home=${base_path}/goctltemplate/v${version} --multiple=true --style=goZero"
  protoc_cmd="protoc ${third_party_proto_paths} ${private_proto_paths} --go_out=${output_path}/pb --go-grpc_out=${output_path}/pb --validate_out="lang=go:${output_path}/pb" --go_opt=module=${module_prefix} --go-grpc_opt=module=${module_prefix} --validate_opt=module=${module_prefix} ${input_path}/*.proto"

  eval ${goctl_cmd}
  eval ${protoc_cmd}
  cd ${base_path}/${project}; make fmt; cd - > /dev/null
}

function model() {
  #goctl model mysql -i "created_at,updated_at,deleted_at" datasource --url="root:Quwan@2020@tcp(127.0.0.1:3306)/${project}" --table="*" --dir="${base_path}/${project}/model" --home="${base_path}/goctltemplate/v${version}" --style="goZero"
  #goctl model mysql -i "created_at,updated_at,deleted_at" datasource --url="root:Quwan@2020@tcp(127.0.0.1:3306)/${project}" --table="*" --dir="${base_path}/${project}/model" -c --home="${base_path}/goctltemplate/v${version}" --style="goZero"
  goctl model mysql -i "created_at,updated_at,deleted_at" datasource --url="probe:Quwan@2020_TTinternation@tcp(10.64.240.41:3306)/${project}" --table="*" --dir="${base_path}/${project}/model" --home="${base_path}/goctltemplate/v${version}" --style="goZero"
}

function help() {
  echo "Usage: $(realpath $0) {api | rpc | model}"
  exit -1
}

function run() {
  goctl_version
  current_module

  case "$1" in
    api)
      api
    ;;
    rpc)
      rpc
    ;;
    model)
      model
    ;;
    *)
      echo "Unknown Command: $1"
      help
    ;;
  esac

  exit 0
}

run "$1"
