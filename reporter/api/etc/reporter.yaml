Name: api.reporter
Host: 0.0.0.0
Port: 20501
#Verbose: true
Timeout: 0

Log:
  ServiceName: api.reporter
  Encoding: plain
  Level: info
  Path: /app/logs/reporter

Prometheus:
  Host: 0.0.0.0
  Port: 20521
  Path: /metrics

Telemetry:
  Name: api.reporter
  Endpoint: http://127.0.0.1:14268/api/traces
  Sampler: 1.0
  Batcher: jaeger

DevServer:
  Enabled: true
  Port: 20531

DB:
#  DataSource: root:Quwan@2020@tcp(127.0.0.1:3306)/reporter?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai
  DataSource: probe:Quwan@2020_TTinternation@tcp(************:3306)/reporter?charset=utf8mb4&parseTime=true&loc=Asia%2FShanghai

Redis:
  Host: 127.0.0.1:6379
  Type: node
  Pass:
  DB: 5

Cache:
  - Host: 127.0.0.1:6379
    Type: node
    Pass:
    DB: 5

Reporter:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.reporter
  Endpoints:
    - 127.0.0.1:20511
  NonBlock: true
  Timeout: 0

User:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.user
  Endpoints:
    - 127.0.0.1:10111
  NonBlock: true
  Timeout: 0

DeviceHub:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.user
  Endpoints:
    - 127.0.0.1:10112
  NonBlock: true
  Timeout: 0

Manager:
#  Etcd:
#    Hosts:
#      - 127.0.0.1:2379
#    Key: rpc.user
  Endpoints:
    - 127.0.0.1:10113
  NonBlock: true
  Timeout: 0

WiseEyes:
  BaseURL: 'https://yw-rd-logs.ttyuyin.com'
  Authorization: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9ZSI6IlQyNzE2I$@@iwi^ZDDsZ.2!'
  Topic: 'quality-testing-svc'

SLA:
  BaseURL: 'http://***********:10089'

ClickPilot:
  BaseURL: 'http://127.0.0.1:8000'
