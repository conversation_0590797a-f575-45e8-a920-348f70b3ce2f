package logic

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	commonpb "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/pb"
	commontypes "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
	commonutils "gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/pb"
)

var (
	apiAPIMetric                   = (*types.APIMetric)(nil)
	rpcAPIMetric                   = (*pb.APIMetric)(nil)
	apiErrMsg                      = (*types.ErrorMessage)(nil)
	rpcErrMsg                      = (*pb.ErrorMessage)(nil)
	apiApplicationConfig           = (*commontypes.ApplicationConfig)(nil)
	rpcApplicationConfig           = (*commonpb.ApplicationConfig)(nil)
	apiUIAgentComponentSteps       = ([]*commontypes.UIAgentComponentStep)(nil)
	rpcUIAgentComponentSteps       = ([]*commonpb.UIAgentComponentStep)(nil)
	apiUIAgentComponentExpectation = (*commontypes.UIAgentComponentExpectation)(nil)
	rpcUIAgentComponentExpectation = (*commonpb.UIAgentComponentExpectation)(nil)
	apiVariables                   = ([]*commontypes.KeyValuePair)(nil)
	rpcVariables                   = ([]*commonpb.GeneralConfigVar)(nil)
	apiUIAgentDevice               = (*commontypes.UIAgentDevice)(nil)
	rpcUIAgentDevice               = (*commonpb.UIAgentDevice)(nil)
)

func ApiAPIMetricToRpcAPIMetric() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiAPIMetric, rpcAPIMetric, nil, nil)
}

func RpcAPIMetricToApiAPIMetric() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcAPIMetric, apiAPIMetric, nil, nil)
}

func ApiErrorMessageToRpcErrorMessage() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiErrMsg, rpcErrMsg, nil, nil)
}

func RpcErrorMessageToApiErrorMessage() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcErrMsg, apiErrMsg, nil, nil)
}

func ApiApplicationConfigToRpcApplicationConfig() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiApplicationConfig, rpcApplicationConfig, nil, nil)
}

func RpcApplicationConfigToApiApplicationConfig() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcApplicationConfig, apiApplicationConfig, nil, nil)
}

func ApiUIAgentComponentStepsToRpcUIAgentComponentSteps() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiUIAgentComponentSteps, rpcUIAgentComponentSteps, nil, nil)
}

func RpcUIAgentComponentStepsToApiUIAgentComponentSteps() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcUIAgentComponentSteps, apiUIAgentComponentSteps, nil, nil)
}

func ApiUIAgentComponentExpectationToRpcUIAgentComponentExpectation() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiUIAgentComponentExpectation, rpcUIAgentComponentExpectation, nil, nil)
}

func RpcUIAgentComponentExpectationToApiUIAgentComponentExpectation() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcUIAgentComponentExpectation, apiUIAgentComponentExpectation, nil, nil)
}

func ApiVariablesToRpcVariables() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiVariables, rpcVariables, nil, nil)
}

func RpcVariablesToApiVariables() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcVariables, apiVariables, nil, nil)
}

func ApiUIAgentDeviceToRpcUIAgentDevice() utils.TypeConverter {
	return commonutils.ApiStructToRpcMessage(apiUIAgentDevice, rpcUIAgentDevice, nil, nil)
}

func RpcUIAgentDeviceToApiUIAgentDevice() utils.TypeConverter {
	return commonutils.RpcMessageToApiStruct(rpcUIAgentDevice, apiUIAgentDevice, nil, nil)
}
