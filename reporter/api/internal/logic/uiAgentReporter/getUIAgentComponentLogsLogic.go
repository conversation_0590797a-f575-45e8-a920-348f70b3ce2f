package uiAgentReporter

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
)

type GetUIAgentComponentLogsLogic struct {
	*BaseLogic
}

func NewGetUIAgentComponentLogsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIAgentComponentLogsLogic {
	return &GetUIAgentComponentLogsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetUIAgentComponentLogsLogic) GetUIAgentComponentLogs(req *types.GetUIAgentComponentLogsReq) (
	resp *types.GetUIAgentComponentLogsResp, err error,
) {
	// todo: add your logic here and delete this line

	return
}
