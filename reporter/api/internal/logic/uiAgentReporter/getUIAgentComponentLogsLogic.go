package uiAgentReporter

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUIAgentComponentLogsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUIAgentComponentLogsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIAgentComponentLogsLogic {
	return &GetUIAgentComponentLogsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUIAgentComponentLogsLogic) GetUIAgentComponentLogs(req *types.GetUIAgentComponentLogsReq) (resp *types.GetUIAgentComponentLogsResp, err error) {
	// todo: add your logic here and delete this line

	return
}
