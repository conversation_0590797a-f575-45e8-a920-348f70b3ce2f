package uiAgentReporter

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUIAgentComponentStepsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUIAgentComponentStepsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIAgentComponentStepsLogic {
	return &GetUIAgentComponentStepsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUIAgentComponentStepsLogic) GetUIAgentComponentSteps(req *types.GetUIAgentComponentStepsReq) (resp *types.GetUIAgentComponentStepsResp, err error) {
	// todo: add your logic here and delete this line

	return
}
