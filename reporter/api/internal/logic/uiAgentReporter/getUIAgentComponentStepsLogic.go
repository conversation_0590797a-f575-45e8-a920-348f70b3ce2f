package uiAgentReporter

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"
)

type GetUIAgentComponentStepsLogic struct {
	*BaseLogic
}

func NewGetUIAgentComponentStepsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIAgentComponentStepsLogic {
	return &GetUIAgentComponentStepsLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *GetUIAgentComponentStepsLogic) GetUIAgentComponentSteps(req *types.GetUIAgentComponentStepsReq) (
	resp *types.GetUIAgentComponentStepsResp, err error,
) {
	// todo: add your logic here and delete this line

	return
}
