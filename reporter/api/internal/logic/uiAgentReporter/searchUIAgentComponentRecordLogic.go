package uiAgentReporter

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type SearchUIAgentComponentRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSearchUIAgentComponentRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchUIAgentComponentRecordLogic {
	return &SearchUIAgentComponentRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SearchUIAgentComponentRecordLogic) SearchUIAgentComponentRecord(req *types.SearchUIAgentComponentRecordReq) (resp *types.SearchUIAgentComponentRecordResp, err error) {
	// todo: add your logic here and delete this line

	return
}
