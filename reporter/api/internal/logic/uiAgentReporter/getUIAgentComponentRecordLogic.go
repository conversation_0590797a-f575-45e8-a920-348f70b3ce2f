package uiAgentReporter

import (
	"context"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUIAgentComponentRecordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUIAgentComponentRecordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUIAgentComponentRecordLogic {
	return &GetUIAgentComponentRecordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUIAgentComponentRecordLogic) GetUIAgentComponentRecord(req *types.GetUIAgentComponentRecordReq) (resp *types.GetUIAgentComponentRecordResp, err error) {
	// todo: add your logic here and delete this line

	return
}
