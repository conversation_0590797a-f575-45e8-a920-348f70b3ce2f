package config

import (
	"fmt"

	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/sla"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/wiseEyes"
)

type Config struct {
	rest.RestConf

	DB        types.DBConfig
	Redis     redis.RedisConf
	Cache     cache.CacheConf
	Validator types.ValidatorConfig

	Reporter  zrpc.RpcClientConf
	User      zrpc.RpcClientConf
	DeviceHub zrpc.RpcClientConf
	Manager   zrpc.RpcClientConf

	WiseEyes   wiseEyes.Config
	SLA        sla.Config
	ClickPilot clickPilot.Config
}

func (c Config) ListenOn() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

func (c Config) LogConfig() logx.LogConf {
	return c.ServiceConf.Log
}
