package svc

import (
	"github.com/zeromicro/go-zero/core/stores/sqlx"
	"github.com/zeromicro/go-zero/zrpc"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/interceptors/clientinterceptors"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"
	userservice "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/clickPilot"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/sla"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/thirdparty/wiseEyes"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/devicehub/rpc/client/deviceservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/client/slathresholdservice"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/config"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/model"
	perfReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/perfreporter"
	apiReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/reporter"
	stabilityReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/stabilityreporter"
	uiAgentReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/uiagentreporter"
	uiReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/rpc/client/uireporter"
)

type ServiceContext struct {
	Config config.Config

	Validator *utils.Validator

	ExecutionRecordModel          model.ExecutionRecordModel
	InterfaceExecutionRecordModel model.InterfaceExecutionRecordModel
	PerfCaseExecutionRecordModel  model.PerfCaseExecutionRecordModel

	ReporterRPC          apiReporter.Reporter
	UIReporterRPC        uiReporter.UIReporter
	PerfReporterRPC      perfReporter.PerfReporter
	StabilityReporterRPC stabilityReporter.StabilityReporter
	UIAgentReporterRPC   uiAgentReporter.UIAgentReporter

	UserRPC       userservice.UserService
	DeviceHubRPC  deviceservice.DeviceService
	ManagerSlaRPC slathresholdservice.SlaThresholdService

	WiseEyesClient   *wiseEyes.Client
	SLAClient        *sla.Client
	ClickPilotClient *clickPilot.Client
}

func NewServiceContext(c config.Config) *ServiceContext {
	sqlConn := sqlx.NewMysql(c.DB.DataSource)

	return &ServiceContext{
		Config: c,

		Validator: utils.NewValidator(c.Validator.Locale),

		ExecutionRecordModel:          model.NewExecutionRecordModel(sqlConn),
		InterfaceExecutionRecordModel: model.NewInterfaceExecutionRecordModel(sqlConn),
		PerfCaseExecutionRecordModel:  model.NewPerfCaseExecutionRecordModel(sqlConn),

		ReporterRPC: apiReporter.NewReporter(
			zrpc.MustNewClient(
				c.Reporter, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		UIReporterRPC: uiReporter.NewUIReporter(
			zrpc.MustNewClient(
				c.Reporter, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		PerfReporterRPC: perfReporter.NewPerfReporter(
			zrpc.MustNewClient(
				c.Reporter, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		StabilityReporterRPC: stabilityReporter.NewStabilityReporter(
			zrpc.MustNewClient(
				c.Reporter, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		UIAgentReporterRPC: uiAgentReporter.NewUIAgentReporter(
			zrpc.MustNewClient(
				c.Reporter, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		UserRPC: userservice.NewUserService(
			zrpc.MustNewClient(
				c.User, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		DeviceHubRPC: deviceservice.NewDeviceService(
			zrpc.MustNewClient(
				c.DeviceHub, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),
		ManagerSlaRPC: slathresholdservice.NewSlaThresholdService(
			zrpc.MustNewClient(
				c.Manager, clientinterceptors.UnaryUserInfoClientOption(),
			),
		),

		WiseEyesClient:   wiseEyes.NewClient(c.WiseEyes),
		SLAClient:        sla.NewClient(c.SLA),
		ClickPilotClient: clickPilot.NewClient(c.ClickPilot),
	}
}
