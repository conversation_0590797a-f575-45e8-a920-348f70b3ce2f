package types

import (
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/sqlbuilder/api"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/types"
)

type UIAgentComponentRecordItem struct {
	TaskId            string                             `json:"task_id"`            // 任务ID
	ExecuteId         string                             `json:"execute_id"`         // 执行ID
	ProjectId         string                             `json:"project_id"`         // 项目ID
	ComponentId       string                             `json:"component_id"`       // 组件ID
	ComponentName     string                             `json:"component_name"`     // 组件名称
	TriggerMode       string                             `json:"trigger_mode"`       // 触发模式（手动、定时、接口）
	TaskType          string                             `json:"task_type"`          // 任务类型（执行、调试）
	ApplicationConfig *types.ApplicationConfig           `json:"application_config"` // 应用配置
	Steps             []*types.UIAgentComponentStep      `json:"steps"`              // 步骤列表
	Expectation       *types.UIAgentComponentExpectation `json:"expectation"`        // 期望结果
	Variables         []*types.KeyValuePair              `json:"variables"`          // 变量列表
	Device            *types.UIAgentDevice               `json:"device"`             // 设备信息
	Reinstall         bool                               `json:"reinstall"`          // 是否重新安装
	Restart           bool                               `json:"restart"`            // 是否重启应用
	Status            string                             `json:"status"`             // 执行状态
	ExecutedBy        *userinfo.FullUserInfo             `json:"executed_by"`        // 执行者
	StartedAt         int64                              `json:"started_at"`         // 开始时间
	EndedAt           int64                              `json:"ended_at"`           // 结束时间
	CostTime          int64                              `json:"cost_time"`          // 执行耗时
	Cleaned           bool                               `json:"cleaned"`            // 是否已被清理
	CreatedAt         int64                              `json:"created_at"`         // 创建时间
	UpdatedAt         int64                              `json:"updated_at"`         // 更新时间
}

type UIAgentComponentStepItem struct {
	Index     int64  `json:"index"`      // 步骤索引
	Name      string `json:"name"`       // 步骤名称
	Thought   string `json:"thought"`    // 决策思考过程
	Action    string `json:"action"`     // 动作
	Status    string `json:"status"`     // 步骤状态
	Image     string `json:"image"`      // 截图路径
	StartedAt int64  `json:"started_at"` // 开始时间
	EndedAt   int64  `json:"ended_at"`   // 结束时间
	CostTime  int64  `json:"cost_time"`  // 执行耗时
}

type UIAgentComponentLogItem struct {
	Content string `json:"content"` // 日志内容
}

type SearchUIAgentComponentRecordReq struct {
	ProjectId   string           `json:"project_id" validate:"required" zh:"项目ID"`
	ComponentId string           `json:"component_id" validate:"required" zh:"组件ID"`
	Condition   *api.Condition   `json:"condition,omitempty,optional" zh:"查询条件"`
	Pagination  *api.Pagination  `json:"pagination,omitempty,optional" zh:"查询分页"`
	Sort        []*api.SortField `json:"sort,omitempty,optional" zh:"查询排序"`
}

type SearchUIAgentComponentRecordResp struct {
	CurrentPage uint64                        `json:"current_page"`
	PageSize    uint64                        `json:"page_size"`
	TotalCount  uint64                        `json:"total_count"`
	TotalPage   uint64                        `json:"total_page"`
	Items       []*UIAgentComponentRecordItem `json:"items"`
}

type GetUIAgentComponentRecordReq struct {
	TaskId    string `json:"task_id" validate:"required" zh:"任务ID"`
	ExecuteId string `json:"execute_id" validate:"required" zh:"执行ID"`
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
}

type GetUIAgentComponentRecordResp struct {
	*UIAgentComponentRecordItem
}

type GetUIAgentComponentStepsReq struct {
	TaskId    string `json:"task_id" validate:"required" zh:"任务ID"`
	ExecuteId string `json:"execute_id" validate:"required" zh:"执行ID"`
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
}

type GetUIAgentComponentStepsResp struct {
	Steps []*UIAgentComponentStepItem `json:"steps"`
}

type GetUIAgentComponentLogsReq struct {
	TaskId    string `json:"task_id" validate:"required" zh:"任务ID"`
	ExecuteId string `json:"execute_id" validate:"required" zh:"执行ID"`
	ProjectId string `json:"project_id" validate:"required" zh:"项目ID"`
}

type GetUIAgentComponentLogsResp struct {
	*UIAgentComponentLogItem
}
