// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.3

package handler

import (
	"net/http"

	perfReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/handler/perfReporter"
	reporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/handler/reporter"
	stabilityReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/handler/stabilityReporter"
	uiAgentReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/handler/uiAgentReporter"
	uiReporter "gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/handler/uiReporter"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/reporter/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/record/component/view",
				Handler: reporter.ViewComponentRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/component_group/list",
				Handler: reporter.ListComponentGroupRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/component_group/get",
				Handler: reporter.GetComponentGroupRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/case/list",
				Handler: reporter.ListCaseRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/case/get",
				Handler: reporter.GetCaseRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/interface/list",
				Handler: reporter.ListInterfaceRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/interface/get",
				Handler: reporter.GetInterfaceRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/suite/list",
				Handler: reporter.ListSuiteRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/suite/get",
				Handler: reporter.GetSuiteRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/service/list",
				Handler: reporter.ListServiceRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/service/get",
				Handler: reporter.GetServiceRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/plan/list",
				Handler: reporter.ListPlanRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/plan/get",
				Handler: reporter.GetPlanRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/plan/get_time_scale",
				Handler: reporter.GetPlanTimeScaleHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/plan/getSummary",
				Handler: reporter.GetPlanSummaryHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/case/fail/for_plan/list",
				Handler: reporter.ListFailCaseForPlanRecordHandler(serverCtx),
			},
		},
		rest.WithPrefix("/reporter/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/record/ui_plan/list",
				Handler: uiReporter.ListUiPlanRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/ui_plan/get",
				Handler: uiReporter.GetUIPlanRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/ui_plan/suite/search",
				Handler: uiReporter.SearchUISuiteRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/ui_suite/case/search",
				Handler: uiReporter.SearchUICaseRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/ui_case/get",
				Handler: uiReporter.GetUICaseRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/ui_case/step/list",
				Handler: uiReporter.ListUICaseStepListHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/ui_case/step/get",
				Handler: uiReporter.GetUICaseStepHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/ui_plan/device/search",
				Handler: uiReporter.SearchUIDeviceRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/ui_plan/device/perf_data/get",
				Handler: uiReporter.GetUIDevicePerfDataHandler(serverCtx),
			},
			{
				// 获取SLA对比数据
				Method:  http.MethodPost,
				Path:    "/record/sla_data/compare",
				Handler: uiReporter.CompareSlaDataHandler(serverCtx),
			},
			{
				// 获取SLA详细数据
				Method:  http.MethodPost,
				Path:    "/record/sla_data/list",
				Handler: uiReporter.ListSlaDataHandler(serverCtx),
			},
			{
				// 获取指定分支的SLA版本列表
				Method:  http.MethodPost,
				Path:    "/record/sla_version/list",
				Handler: uiReporter.ListSlaVersionHandler(serverCtx),
			},
		},
		rest.WithPrefix("/reporter/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/record/perf_plan/search",
				Handler: perfReporter.SearchPerfPlanRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/perf_plan/get",
				Handler: perfReporter.GetPerfPlanRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/perf_plan/get_v2",
				Handler: perfReporter.GetPerfPlanRecordV2Handler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/perf_plan/case/search",
				Handler: perfReporter.SearchPerfCaseRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/perf_case/log/get",
				Handler: perfReporter.GetPerfCaseLogHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/record/perf_plan/list",
				Handler: perfReporter.ListPerfPlanRecordHandler(serverCtx),
			},
		},
		rest.WithPrefix("/reporter/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// 获取稳测计划的执行记录
				Method:  http.MethodPost,
				Path:    "/record/stability_plan/list",
				Handler: stabilityReporter.ListStabilityPlanRecordHandler(serverCtx),
			},
			{
				// 搜索稳测的执行记录
				Method:  http.MethodPost,
				Path:    "/record/stability_plan/search",
				Handler: stabilityReporter.SearchStabilityPlanRecordHandler(serverCtx),
			},
			{
				// 获取稳测执行报告的计划信息
				Method:  http.MethodGet,
				Path:    "/record/stability_plan/get",
				Handler: stabilityReporter.GetStabilityPlanRecordHandler(serverCtx),
			},
			{
				// 搜索稳测执行报告的总览设备
				Method:  http.MethodPost,
				Path:    "/record/stability_device/search",
				Handler: stabilityReporter.SearchStabilityDeviceRecordHandler(serverCtx),
			},
			{
				// 获取稳测执行报告的设备步骤日志
				Method:  http.MethodPost,
				Path:    "/record/stability_device/step/list",
				Handler: stabilityReporter.ListStabilityDeviceStepHandler(serverCtx),
			},
			{
				// 获取稳测执行报告的设备性能数据
				Method:  http.MethodGet,
				Path:    "/record/stability_device/perf_data/get",
				Handler: stabilityReporter.GetStabilityDevicePerfDataHandler(serverCtx),
			},
			{
				// 获取稳测执行报告的设备Activity统计
				Method:  http.MethodGet,
				Path:    "/record/stability_device/activity/get",
				Handler: stabilityReporter.GetStabilityDeviceActivityHandler(serverCtx),
			},
		},
		rest.WithPrefix("/reporter/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/record/ui_agent_component/search",
				Handler: uiAgentReporter.SearchUIAgentComponentRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/ui_agent_component/get",
				Handler: uiAgentReporter.GetUIAgentComponentRecordHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/ui_agent_component/step/get",
				Handler: uiAgentReporter.GetUIAgentComponentStepsHandler(serverCtx),
			},
			{
				Method:  http.MethodGet,
				Path:    "/record/ui_agent_component/log/get",
				Handler: uiAgentReporter.GetUIAgentComponentLogsHandler(serverCtx),
			},
		},
		rest.WithPrefix("/reporter/v1"),
	)
}
