// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.shopping.css.v1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Shopping.Css.V1";
option go_package = "cloud.google.com/go/shopping/css/apiv1/csspb;csspb";
option java_multiple_files = true;
option java_outer_classname = "AccountsProto";
option java_package = "com.google.shopping.css.v1";
option php_namespace = "Google\\Shopping\\Css\\V1";
option ruby_package = "Google::Shopping::Css::V1";

// Service for managing CSS/MC account information.
service AccountsService {
  option (google.api.default_host) = "css.googleapis.com";
  option (google.api.oauth_scopes) = "https://www.googleapis.com/auth/content";

  // Lists all the accounts under the specified CSS account ID, and
  // optionally filters by label ID and account name.
  rpc ListChildAccounts(ListChildAccountsRequest)
      returns (ListChildAccountsResponse) {
    option (google.api.http) = {
      get: "/v1/{parent=accounts/*}:listChildAccounts"
    };
    option (google.api.method_signature) = "parent";
  }

  // Retrieves a single CSS/MC account by ID.
  rpc GetAccount(GetAccountRequest) returns (Account) {
    option (google.api.http) = {
      get: "/v1/{name=accounts/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Updates labels assigned to CSS/MC accounts by a CSS domain.
  rpc UpdateLabels(UpdateAccountLabelsRequest) returns (Account) {
    option (google.api.http) = {
      post: "/v1/{name=accounts/*}:updateLabels"
      body: "*"
    };
    option (google.api.method_signature) = "name";
  }
}

// The request message for the `ListChildAccounts` method.
message ListChildAccountsRequest {
  // Required. The parent account. Must be a CSS group or domain.
  // Format: accounts/{account}
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "css.googleapis.com/Account" }
  ];

  // If set, only the MC accounts with the given label ID will be returned.
  optional int64 label_id = 2;

  // If set, only the MC accounts with the given name (case sensitive) will be
  // returned.
  optional string full_name = 3;

  // Optional. The maximum number of accounts to return. The service may return
  // fewer than this value. If unspecified, at most 50 accounts will be
  // returned. The maximum value is 100; values above 100 will be coerced to
  // 100.
  int32 page_size = 4 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A page token, received from a previous `ListChildAccounts` call.
  // Provide this to retrieve the subsequent page.
  //
  // When paginating, all other parameters provided to `ListChildAccounts` must
  // match the call that provided the page token.
  string page_token = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for the `ListChildAccounts` method.
message ListChildAccountsResponse {
  // The CSS/MC accounts returned for the specified CSS parent account.
  repeated Account accounts = 1;

  // A token, which can be sent as `page_token` to retrieve the next page.
  // If this field is omitted, there are no subsequent pages.
  string next_page_token = 2;
}

// The request message for the `GetAccount` method.
message GetAccountRequest {
  // Required. The name of the managed CSS/MC account.
  // Format: accounts/{account}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "css.googleapis.com/Account" }
  ];

  // Optional. Only required when retrieving MC account information.
  // The CSS domain that is the parent resource of the MC account.
  // Format: accounts/{account}
  optional string parent = 2 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "css.googleapis.com/Account" }
  ];
}

// The request message for the `UpdateLabels` method.
message UpdateAccountLabelsRequest {
  // Required. The label resource name.
  // Format: accounts/{account}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "css.googleapis.com/Account" }
  ];

  // The list of label IDs to overwrite the existing account label IDs.
  // If the list is empty, all currently assigned label IDs will be deleted.
  repeated int64 label_ids = 2;

  // Optional. Only required when updating MC account labels.
  // The CSS domain that is the parent resource of the MC account.
  // Format: accounts/{account}
  optional string parent = 3 [
    (google.api.field_behavior) = OPTIONAL,
    (google.api.resource_reference) = { type: "css.googleapis.com/Account" }
  ];
}

// Information about CSS/MC account.
message Account {
  option (google.api.resource) = {
    type: "css.googleapis.com/Account"
    pattern: "accounts/{account}"
    plural: "accounts"
    singular: "account"
  };

  // The account type.
  enum AccountType {
    // Unknown account type.
    ACCOUNT_TYPE_UNSPECIFIED = 0;

    // CSS group account.
    CSS_GROUP = 1;

    // CSS domain account.
    CSS_DOMAIN = 2;

    // MC Primary CSS MCA account.
    MC_PRIMARY_CSS_MCA = 3;

    // MC CSS MCA account.
    MC_CSS_MCA = 4;

    // MC Marketplace MCA account.
    MC_MARKETPLACE_MCA = 5;

    // MC Other MCA account.
    MC_OTHER_MCA = 6;

    // MC Standalone account.
    MC_STANDALONE = 7;

    // MC MCA sub-account.
    MC_MCA_SUBACCOUNT = 8;
  }

  // The label resource name.
  // Format: accounts/{account}
  string name = 1;

  // Output only. Immutable. The CSS/MC account's full name.
  string full_name = 2 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The CSS/MC account's short display name.
  optional string display_name = 3;

  // Output only. Immutable. The CSS/MC account's homepage.
  optional string homepage_uri = 4 [
    (google.api.field_behavior) = OUTPUT_ONLY,
    (google.api.field_behavior) = IMMUTABLE
  ];

  // The CSS/MC account's parent resource. CSS group for CSS domains; CSS
  // domain for MC accounts. Returned only if the user has access to the
  // parent account.
  optional string parent = 5;

  // Manually created label IDs assigned to the CSS/MC account by a CSS parent
  // account.
  repeated int64 label_ids = 6;

  // Automatically created label IDs assigned to the MC account by
  // CSS Center.
  repeated int64 automatic_label_ids = 7;

  // Output only. The type of this account.
  AccountType account_type = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}
