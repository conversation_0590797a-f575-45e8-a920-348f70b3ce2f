// Copyright 2023 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v16.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V16.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v16/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "LocalServicesVerificationArtifactStatusProto";
option java_package = "com.google.ads.googleads.v16.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V16\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V16::Enums";

// Container for enum describing the status of local services verification
// artifact.
message LocalServicesVerificationArtifactStatusEnum {
  // Enums describing statuses of a local services verification artifact.
  enum LocalServicesVerificationArtifactStatus {
    // Not specified.
    UNSPECIFIED = 0;

    // Used for return value only. Represents value unknown in this version.
    UNKNOWN = 1;

    // Artifact has passed verification.
    PASSED = 2;

    // Artifact has failed verification.
    FAILED = 3;

    // Artifact is in the process of verification.
    PENDING = 4;

    // Artifact needs user to upload information before it is verified.
    NO_SUBMISSION = 5;

    // Artifact has been cancelled by the user.
    CANCELLED = 6;
  }
}
