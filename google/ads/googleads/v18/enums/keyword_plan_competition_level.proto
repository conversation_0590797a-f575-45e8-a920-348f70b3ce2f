// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.ads.googleads.v18.enums;

option csharp_namespace = "Google.Ads.GoogleAds.V18.Enums";
option go_package = "google.golang.org/genproto/googleapis/ads/googleads/v18/enums;enums";
option java_multiple_files = true;
option java_outer_classname = "KeywordPlanCompetitionLevelProto";
option java_package = "com.google.ads.googleads.v18.enums";
option objc_class_prefix = "GAA";
option php_namespace = "Google\\Ads\\GoogleAds\\V18\\Enums";
option ruby_package = "Google::Ads::GoogleAds::V18::Enums";

// Proto file describing Keyword Planner competition levels.

// Container for enumeration of keyword competition levels. The competition
// level indicates how competitive ad placement is for a keyword and
// is determined by the number of advertisers bidding on that keyword relative
// to all keywords across Google. The competition level can depend on the
// location and Search Network targeting options you've selected.
message KeywordPlanCompetitionLevelEnum {
  // Competition level of a keyword.
  enum KeywordPlanCompetitionLevel {
    // Not specified.
    UNSPECIFIED = 0;

    // The value is unknown in this version.
    UNKNOWN = 1;

    // Low competition. The Competition Index range for this is [0, 33].
    LOW = 2;

    // Medium competition. The Competition Index range for this is [34, 66].
    MEDIUM = 3;

    // High competition. The Competition Index range for this is [67, 100].
    HIGH = 4;
  }
}
