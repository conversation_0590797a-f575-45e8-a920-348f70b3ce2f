// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.networkanalyzer.logging.v1;

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.NetworkAnalyzer.Logging.V1";
option go_package = "cloud.google.com/go/networkanalyzer/logging/apiv1/loggingpb;loggingpb";
option java_multiple_files = true;
option java_outer_classname = "AnalyzerLogProto";
option java_package = "com.google.cloud.networkanalyzer.logging.v1";
option php_namespace = "Google\\Cloud\\NetworkAnalyzer\\Logging\\V1";
option ruby_package = "Google::Cloud::NetworkAnalyzer::Logging::V1";

enum ReportCauseCode {
  REPORT_CAUSE_CODE_UNSPECIFIED = 0;

  // VPC Basics
  ROUTE_INVALID_NEXT_HOP_VM_IP_FORWARDING_DISABLED = 1;

  ROUTE_INVALID_NEXT_HOP_VM_DELETED = 2;

  ROUTE_INVALID_NEXT_HOP_VM_STOPPED = 3;

  ROUTE_INVALID_NEXT_HOP_ILB_MISCONFIGURED = 4;

  ROUTE_INVALID_NEXT_HOP_VPN_TUNNEL_DELETED = 5;

  ROUTE_INVALID_NEXT_HOP_ILB_BACKEND_IP_FORWARDING_DISABLED = 6;

  ROUTE_INVALID_NEXT_HOP_ILB_VIP_NOT_ASSIGNED = 7;

  IP_UTILIZATION_IP_ALLOCATION_RATIO_HIGH = 20;

  // Summary of ip utilization of all subnet ranges in the project.
  IP_UTILIZATION_IP_ALLOCATION_SUMMARY = 21;

  // Kubernetes Engine
  GKE_NODE_TO_CONTROL_PLANE_BLOCKED_BY_ROUTING_ISSUE = 201;

  GKE_NODE_TO_CONTROL_PLANE_PUBLIC_ENDPOINT_BLOCKED_BY_EGRESS_FIREWALL = 202;

  GKE_NODE_TO_CONTROL_PLANE_PRIVATE_ENDPOINT_BLOCKED_BY_EGRESS_FIREWALL = 203;

  GKE_CONTROL_PLANE_TO_NODE_BLOCKED_BY_ROUTING_ISSUE = 211;

  GKE_CONTROL_PLANE_TO_NODE_BLOCKED_BY_INGRESS_FIREWALL_ON_NODE = 212;

  GKE_IP_UTILIZATION_POD_RANGES_ALLOCATION_HIGH = 221;

  GKE_IP_UTILIZATION_POD_RANGES_ALLOCATION_LIMITES_AUTOSCALING = 222;

  GKE_NODE_SERVICE_ACCOUNT_SERVICE_ACCOUNT_DISABLED = 243;

  GKE_NODE_SERVICE_ACCOUNT_DEFAULT_SERVICE_ACCOUNT_USED = 244;

  GKE_NODE_SERVICE_ACCOUNT_BAD_OAUTH_SCOPES = 245;

  // Managed Services
  CLOUD_SQL_PRIVATE_IP_BLOCKED_BY_EGRESS_FIREWALL = 601;

  CLOUD_SQL_PRIVATE_IP_BLOCKED_BY_ROUTING_ISSUE = 602;

  CLOUD_SQL_PRIVATE_IP_INSTANCE_NOT_RUNNING = 603;

  // Hybrid Connectivity
  DYNAMIC_ROUTE_SHADOWED_FULLY_SHADOWED_BY_SUBNET_ROUTE = 801;

  DYNAMIC_ROUTE_SHADOWED_FULLY_SHADOWED_BY_PEERING_SUBNET_ROUTE = 802;

  DYNAMIC_ROUTE_SHADOWED_FULLY_SHADOWED_BY_STATIC_ROUTE = 803;

  DYNAMIC_ROUTE_SHADOWED_FULLY_SHADOWED_BY_PEERING_STATIC_ROUTE = 804;

  DYNAMIC_ROUTE_SHADOWED_PARTIALLY_SHADOWED_BY_SUBNET_ROUTE = 805;

  DYNAMIC_ROUTE_SHADOWED_PARTIALLY_SHADOWED_BY_PEERING_SUBNET_ROUTE = 806;

  DYNAMIC_ROUTE_SHADOWED_PARTIALLY_SHADOWED_BY_STATIC_ROUTE = 807;

  DYNAMIC_ROUTE_SHADOWED_PARTIALLY_SHADOWED_BY_PEERING_STATIC_ROUTE = 808;

  // Network Services
  LOAD_BALANCER_HEALTH_CHECK_FIREWALL_HEALTH_CHECK_FIREWALL_NOT_CONFIGURED =
      1001;

  LOAD_BALANCER_HEALTH_CHECK_FIREWALL_HEALTH_CHECK_RANGE_BLOCKED = 1002;

  LOAD_BALANCER_HEALTH_CHECK_FIREWALL_FIREWALL_CONFIG_INCONSISTENT = 1003;

  LOAD_BALANCER_HEALTH_CHECK_FIREWALL_HEALTH_CHECK_RANGE_PARTIALLY_BLOCKED =
      1004;

  LOAD_BALANCER_BEST_PRACTICES_BACKEND_SERVICE_BALANCING_MODE_BREAKS_SESSION_AFFINITY =
      1021;

  LOAD_BALANCER_BEST_PRACTICES_BACKEND_SERVICE_HEALTH_CHECK_PORT_MISMATCH =
      1024;
}

message IpUtilizationInfo {
  message SubnetIpUtilization {
    // URI of subnet.
    string subnet_uri = 1;

    // Secondary range name. If the range is the primary range of the subnet,
    // this field is empty.
    string secondary_range_name = 2;

    // Total number of usable IP addresses in the IP range.
    uint64 total_usable_addresses = 3;

    // The ratio of allocated IP addresses from the total usable addresses.
    double allocation_ratio = 4;
  }

  repeated SubnetIpUtilization subnet_ip_utilization = 1;
}

// Log entry that describes a report from Network Analyzer.
message Report {
  // Priority level of an report.
  enum Priority {
    SEVERITY_UNSPECIFIED = 0;

    CRITICAL = 1;

    HIGH = 2;

    MEDIUM = 3;

    LOW = 4;
  }

  // Type of an report.
  enum Type {
    REPORT_TYPE_UNSPECIFIED = 0;

    INFO = 1;

    WARNING = 2;

    ERROR = 3;
  }

  // Status of an report.
  enum ReportStatus {
    REPORT_STATUS_UNSPECIFIED = 0;

    ACTIVE = 1;

    FIXED = 2;

    DISMISSED = 3;
  }

  // Groups of an report.
  enum ReportGroup {
    CATEGORY_UNSPECIFIED = 0;

    VPC_NETWORK = 1;

    NETWORK_SERVICES = 2;

    KUBERNETES_ENGINE = 3;

    HYBRID_CONNECTIVITY = 4;

    MANAGED_SERVICES = 5;
  }

  // The unique identifier of the report.
  string id = 1;

  // Priority of the report.
  Priority priority = 2;

  // Type of the report.
  Type type = 3;

  // Status of the report.
  ReportStatus status = 4;

  // The timestamp when the report was first discovered by Network Analyzer.
  google.protobuf.Timestamp first_report_time = 9;

  // Cause code of the report.
  ReportCauseCode cause_code = 12;

  // The resource that are reported with the report.
  // Contains the fully qualified resource name.
  // e.g.,
  // `//compute.googleapis.com/projects/{project_id}/global/networks/{network}`
  string resource_name = 15;

  // Location associated with the report. It can be global or GCP regions
  // and zones. e.g., https://cloud.google.com/compute/docs/regions-zones/
  string location = 16;

  // URI to the documentation of the report.
  string report_documentation_uri = 17;

  // The groups of the report. One report may be present in multiple groups.
  repeated ReportGroup report_groups = 18;

  oneof content {
    IpUtilizationInfo ip_utilization_info = 19;
  }
}
