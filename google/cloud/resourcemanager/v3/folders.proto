// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.resourcemanager.v3;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";
import "google/iam/v1/iam_policy.proto";
import "google/iam/v1/policy.proto";
import "google/longrunning/operations.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Google.Cloud.ResourceManager.V3";
option go_package = "cloud.google.com/go/resourcemanager/apiv3/resourcemanagerpb;resourcemanagerpb";
option java_multiple_files = true;
option java_outer_classname = "FoldersProto";
option java_package = "com.google.cloud.resourcemanager.v3";
option php_namespace = "Google\\Cloud\\ResourceManager\\V3";
option ruby_package = "Google::Cloud::ResourceManager::V3";

// Manages Cloud Platform folder resources.
// Folders can be used to organize the resources under an
// organization and to control the policies applied to groups of resources.
service Folders {
  option (google.api.default_host) = "cloudresourcemanager.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform,"
      "https://www.googleapis.com/auth/cloud-platform.read-only";

  // Retrieves a folder identified by the supplied resource name.
  // Valid folder resource names have the format `folders/{folder_id}`
  // (for example, `folders/1234`).
  // The caller must have `resourcemanager.folders.get` permission on the
  // identified folder.
  rpc GetFolder(GetFolderRequest) returns (Folder) {
    option (google.api.http) = {
      get: "/v3/{name=folders/*}"
    };
    option (google.api.method_signature) = "name";
  }

  // Lists the folders that are direct descendants of supplied parent resource.
  // `list()` provides a strongly consistent view of the folders underneath
  // the specified parent resource.
  // `list()` returns folders sorted based upon the (ascending) lexical ordering
  // of their display_name.
  // The caller must have `resourcemanager.folders.list` permission on the
  // identified parent.
  rpc ListFolders(ListFoldersRequest) returns (ListFoldersResponse) {
    option (google.api.http) = {
      get: "/v3/folders"
    };
    option (google.api.method_signature) = "parent";
  }

  // Search for folders that match specific filter criteria.
  // `search()` provides an eventually consistent view of the folders a user has
  // access to which meet the specified filter criteria.
  //
  // This will only return folders on which the caller has the
  // permission `resourcemanager.folders.get`.
  rpc SearchFolders(SearchFoldersRequest) returns (SearchFoldersResponse) {
    option (google.api.http) = {
      get: "/v3/folders:search"
    };
    option (google.api.method_signature) = "query";
  }

  // Creates a folder in the resource hierarchy.
  // Returns an `Operation` which can be used to track the progress of the
  // folder creation workflow.
  // Upon success, the `Operation.response` field will be populated with the
  // created Folder.
  //
  // In order to succeed, the addition of this new folder must not violate
  // the folder naming, height, or fanout constraints.
  //
  // + The folder's `display_name` must be distinct from all other folders that
  // share its parent.
  // + The addition of the folder must not cause the active folder hierarchy
  // to exceed a height of 10. Note, the full active + deleted folder hierarchy
  // is allowed to reach a height of 20; this provides additional headroom when
  // moving folders that contain deleted folders.
  // + The addition of the folder must not cause the total number of folders
  // under its parent to exceed 300.
  //
  // If the operation fails due to a folder constraint violation, some errors
  // may be returned by the `CreateFolder` request, with status code
  // `FAILED_PRECONDITION` and an error description. Other folder constraint
  // violations will be communicated in the `Operation`, with the specific
  // `PreconditionFailure` returned in the details list in the `Operation.error`
  // field.
  //
  // The caller must have `resourcemanager.folders.create` permission on the
  // identified parent.
  rpc CreateFolder(CreateFolderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/folders"
      body: "folder"
    };
    option (google.api.method_signature) = "folder";
    option (google.longrunning.operation_info) = {
      response_type: "Folder"
      metadata_type: "CreateFolderMetadata"
    };
  }

  // Updates a folder, changing its `display_name`.
  // Changes to the folder `display_name` will be rejected if they violate
  // either the `display_name` formatting rules or the naming constraints
  // described in the
  // [CreateFolder][google.cloud.resourcemanager.v3.Folders.CreateFolder]
  // documentation.
  //
  // The folder's `display_name` must start and end with a letter or digit,
  // may contain letters, digits, spaces, hyphens and underscores and can be
  // between 3 and 30 characters. This is captured by the regular expression:
  // `[\p{L}\p{N}][\p{L}\p{N}_- ]{1,28}[\p{L}\p{N}]`.
  // The caller must have `resourcemanager.folders.update` permission on the
  // identified folder.
  //
  // If the update fails due to the unique name constraint then a
  // `PreconditionFailure` explaining this violation will be returned
  // in the Status.details field.
  rpc UpdateFolder(UpdateFolderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      patch: "/v3/{folder.name=folders/*}"
      body: "folder"
    };
    option (google.api.method_signature) = "folder,update_mask";
    option (google.longrunning.operation_info) = {
      response_type: "Folder"
      metadata_type: "UpdateFolderMetadata"
    };
  }

  // Moves a folder under a new resource parent.
  // Returns an `Operation` which can be used to track the progress of the
  // folder move workflow.
  // Upon success, the `Operation.response` field will be populated with the
  // moved folder.
  // Upon failure, a `FolderOperationError` categorizing the failure cause will
  // be returned - if the failure occurs synchronously then the
  // `FolderOperationError` will be returned in the `Status.details` field.
  // If it occurs asynchronously, then the FolderOperation will be returned
  // in the `Operation.error` field.
  // In addition, the `Operation.metadata` field will be populated with a
  // `FolderOperation` message as an aid to stateless clients.
  // Folder moves will be rejected if they violate either the naming, height,
  // or fanout constraints described in the
  // [CreateFolder][google.cloud.resourcemanager.v3.Folders.CreateFolder]
  // documentation. The caller must have `resourcemanager.folders.move`
  // permission on the folder's current and proposed new parent.
  rpc MoveFolder(MoveFolderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{name=folders/*}:move"
      body: "*"
    };
    option (google.api.method_signature) = "name,destination_parent";
    option (google.longrunning.operation_info) = {
      response_type: "Folder"
      metadata_type: "MoveFolderMetadata"
    };
  }

  // Requests deletion of a folder. The folder is moved into the
  // [DELETE_REQUESTED][google.cloud.resourcemanager.v3.Folder.State.DELETE_REQUESTED]
  // state immediately, and is deleted approximately 30 days later. This method
  // may only be called on an empty folder, where a folder is empty if it
  // doesn't contain any folders or projects in the
  // [ACTIVE][google.cloud.resourcemanager.v3.Folder.State.ACTIVE] state. If
  // called on a folder in
  // [DELETE_REQUESTED][google.cloud.resourcemanager.v3.Folder.State.DELETE_REQUESTED]
  // state the operation will result in a no-op success.
  // The caller must have `resourcemanager.folders.delete` permission on the
  // identified folder.
  rpc DeleteFolder(DeleteFolderRequest) returns (google.longrunning.Operation) {
    option (google.api.http) = {
      delete: "/v3/{name=folders/*}"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Folder"
      metadata_type: "DeleteFolderMetadata"
    };
  }

  // Cancels the deletion request for a folder. This method may be called on a
  // folder in any state. If the folder is in the
  // [ACTIVE][google.cloud.resourcemanager.v3.Folder.State.ACTIVE] state the
  // result will be a no-op success. In order to succeed, the folder's parent
  // must be in the
  // [ACTIVE][google.cloud.resourcemanager.v3.Folder.State.ACTIVE] state. In
  // addition, reintroducing the folder into the tree must not violate folder
  // naming, height, and fanout constraints described in the
  // [CreateFolder][google.cloud.resourcemanager.v3.Folders.CreateFolder]
  // documentation. The caller must have `resourcemanager.folders.undelete`
  // permission on the identified folder.
  rpc UndeleteFolder(UndeleteFolderRequest)
      returns (google.longrunning.Operation) {
    option (google.api.http) = {
      post: "/v3/{name=folders/*}:undelete"
      body: "*"
    };
    option (google.api.method_signature) = "name";
    option (google.longrunning.operation_info) = {
      response_type: "Folder"
      metadata_type: "UndeleteFolderMetadata"
    };
  }

  // Gets the access control policy for a folder. The returned policy may be
  // empty if no such policy or resource exists. The `resource` field should
  // be the folder's resource name, for example: "folders/1234".
  // The caller must have `resourcemanager.folders.getIamPolicy` permission
  // on the identified folder.
  rpc GetIamPolicy(google.iam.v1.GetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v3/{resource=folders/*}:getIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource";
  }

  // Sets the access control policy on a folder, replacing any existing policy.
  // The `resource` field should be the folder's resource name, for example:
  // "folders/1234".
  // The caller must have `resourcemanager.folders.setIamPolicy` permission
  // on the identified folder.
  rpc SetIamPolicy(google.iam.v1.SetIamPolicyRequest)
      returns (google.iam.v1.Policy) {
    option (google.api.http) = {
      post: "/v3/{resource=folders/*}:setIamPolicy"
      body: "*"
    };
    option (google.api.method_signature) = "resource,policy";
  }

  // Returns permissions that a caller has on the specified folder.
  // The `resource` field should be the folder's resource name,
  // for example: "folders/1234".
  //
  // There are no permissions required for making this API call.
  rpc TestIamPermissions(google.iam.v1.TestIamPermissionsRequest)
      returns (google.iam.v1.TestIamPermissionsResponse) {
    option (google.api.http) = {
      post: "/v3/{resource=folders/*}:testIamPermissions"
      body: "*"
    };
    option (google.api.method_signature) = "resource,permissions";
  }
}

// A folder in an organization's resource hierarchy, used to
// organize that organization's resources.
message Folder {
  option (google.api.resource) = {
    type: "cloudresourcemanager.googleapis.com/Folder"
    pattern: "folders/{folder}"
    style: DECLARATIVE_FRIENDLY
  };

  // Folder lifecycle states.
  enum State {
    // Unspecified state.
    STATE_UNSPECIFIED = 0;

    // The normal and active state.
    ACTIVE = 1;

    // The folder has been marked for deletion by the user.
    DELETE_REQUESTED = 2;
  }

  // Output only. The resource name of the folder.
  // Its format is `folders/{folder_id}`, for example: "folders/1234".
  string name = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Required. The folder's parent's resource name.
  // Updates to the folder's parent must be performed using
  // [MoveFolder][google.cloud.resourcemanager.v3.Folders.MoveFolder].
  string parent = 2 [(google.api.field_behavior) = REQUIRED];

  // The folder's display name.
  // A folder's display name must be unique amongst its siblings. For example,
  // no two folders with the same parent can share the same display name.
  // The display name must start and end with a letter or digit, may contain
  // letters, digits, spaces, hyphens and underscores and can be no longer
  // than 30 characters. This is captured by the regular expression:
  // `[\p{L}\p{N}]([\p{L}\p{N}_- ]{0,28}[\p{L}\p{N}])?`.
  string display_name = 3;

  // Output only. The lifecycle state of the folder.
  // Updates to the state must be performed using
  // [DeleteFolder][google.cloud.resourcemanager.v3.Folders.DeleteFolder] and
  // [UndeleteFolder][google.cloud.resourcemanager.v3.Folders.UndeleteFolder].
  State state = 4 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the folder was created.
  google.protobuf.Timestamp create_time = 5
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the folder was last modified.
  google.protobuf.Timestamp update_time = 6
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. Timestamp when the folder was requested to be deleted.
  google.protobuf.Timestamp delete_time = 7
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // Output only. A checksum computed by the server based on the current value
  // of the folder resource. This may be sent on update and delete requests to
  // ensure the client has an up-to-date value before proceeding.
  string etag = 8 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// The GetFolder request message.
message GetFolderRequest {
  // Required. The resource name of the folder to retrieve.
  // Must be of the form `folders/{folder_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Folder"
    }
  ];
}

// The ListFolders request message.
message ListFoldersRequest {
  // Required. The name of the parent resource whose folders are being listed.
  // Only children of this parent resource are listed; descendants are not
  // listed.
  //
  // If the parent is a folder, use the value `folders/{folder_id}`. If the
  // parent is an organization, use the value `organizations/{org_id}`.
  //
  // Access to this method is controlled by checking the
  // `resourcemanager.folders.list` permission on the `parent`.
  string parent = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { child_type: "*" }
  ];

  // Optional. The maximum number of folders to return in the response. The
  // server can return fewer folders than requested. If unspecified, server
  // picks an appropriate default.
  int32 page_size = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous call to `ListFolders`
  // that indicates where this listing should continue from.
  string page_token = 3 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Controls whether folders in the
  // [DELETE_REQUESTED][google.cloud.resourcemanager.v3.Folder.State.DELETE_REQUESTED]
  // state should be returned. Defaults to false.
  bool show_deleted = 4 [(google.api.field_behavior) = OPTIONAL];
}

// The ListFolders response message.
message ListFoldersResponse {
  // A possibly paginated list of folders that are direct descendants of
  // the specified parent resource.
  repeated Folder folders = 1;

  // A pagination token returned from a previous call to `ListFolders`
  // that indicates from where listing should continue.
  string next_page_token = 2;
}

// The request message for searching folders.
message SearchFoldersRequest {
  // Optional. The maximum number of folders to return in the response. The
  // server can return fewer folders than requested. If unspecified, server
  // picks an appropriate default.
  int32 page_size = 1 [(google.api.field_behavior) = OPTIONAL];

  // Optional. A pagination token returned from a previous call to
  // `SearchFolders` that indicates from where search should continue.
  string page_token = 2 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Search criteria used to select the folders to return.
  // If no search criteria is specified then all accessible folders will be
  // returned.
  //
  // Query expressions can be used to restrict results based upon displayName,
  // state and parent, where the operators `=` (`:`) `NOT`, `AND` and `OR`
  // can be used along with the suffix wildcard symbol `*`.
  //
  // The `displayName` field in a query expression should use escaped quotes
  // for values that include whitespace to prevent unexpected behavior.
  //
  // ```
  // | Field                   | Description                            |
  // |-------------------------|----------------------------------------|
  // | displayName             | Filters by displayName.                |
  // | parent                  | Filters by parent (for example: folders/123). |
  // | state, lifecycleState   | Filters by state.                      |
  // ```
  //
  // Some example queries are:
  //
  // * Query `displayName=Test*` returns Folder resources whose display name
  // starts with "Test".
  // * Query `state=ACTIVE` returns Folder resources with
  // `state` set to `ACTIVE`.
  // * Query `parent=folders/123` returns Folder resources that have
  // `folders/123` as a parent resource.
  // * Query `parent=folders/123 AND state=ACTIVE` returns active
  // Folder resources that have `folders/123` as a parent resource.
  // * Query `displayName=\\"Test String\\"` returns Folder resources with
  // display names that include both "Test" and "String".
  string query = 3 [(google.api.field_behavior) = OPTIONAL];
}

// The response message for searching folders.
message SearchFoldersResponse {
  // A possibly paginated folder search results.
  // the specified parent resource.
  repeated Folder folders = 1;

  // A pagination token returned from a previous call to `SearchFolders`
  // that indicates from where searching should continue.
  string next_page_token = 2;
}

// The CreateFolder request message.
message CreateFolderRequest {
  // Required. The folder being created, only the display name and parent will
  // be consulted. All other fields will be ignored.
  Folder folder = 2 [(google.api.field_behavior) = REQUIRED];
}

// Metadata pertaining to the Folder creation process.
message CreateFolderMetadata {
  // The display name of the folder.
  string display_name = 1;

  // The resource name of the folder or organization we are creating the folder
  // under.
  string parent = 2;
}

// The request sent to the
// [UpdateFolder][google.cloud.resourcemanager.v3.Folder.UpdateFolder]
// method.
//
// Only the `display_name` field can be changed. All other fields will be
// ignored. Use the
// [MoveFolder][google.cloud.resourcemanager.v3.Folders.MoveFolder] method to
// change the `parent` field.
message UpdateFolderRequest {
  // Required. The new definition of the Folder. It must include the `name`
  // field, which cannot be changed.
  Folder folder = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. Fields to be updated.
  // Only the `display_name` can be updated.
  google.protobuf.FieldMask update_mask = 2
      [(google.api.field_behavior) = REQUIRED];
}

// A status object which is used as the `metadata` field for the Operation
// returned by UpdateFolder.
message UpdateFolderMetadata {}

// The MoveFolder request message.
message MoveFolderRequest {
  // Required. The resource name of the Folder to move.
  // Must be of the form folders/{folder_id}
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Folder"
    }
  ];

  // Required. The resource name of the folder or organization which should be
  // the folder's new parent. Must be of the form `folders/{folder_id}` or
  // `organizations/{org_id}`.
  string destination_parent = 2 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { child_type: "*" }
  ];
}

// Metadata pertaining to the folder move process.
message MoveFolderMetadata {
  // The display name of the folder.
  string display_name = 1;

  // The resource name of the folder's parent.
  string source_parent = 2;

  // The resource name of the folder or organization to move the folder to.
  string destination_parent = 3;
}

// The DeleteFolder request message.
message DeleteFolderRequest {
  // Required. The resource name of the folder to be deleted.
  // Must be of the form `folders/{folder_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Folder"
    }
  ];
}

// A status object which is used as the `metadata` field for the `Operation`
// returned by `DeleteFolder`.
message DeleteFolderMetadata {}

// The UndeleteFolder request message.
message UndeleteFolderRequest {
  // Required. The resource name of the folder to undelete.
  // Must be of the form `folders/{folder_id}`.
  string name = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = {
      type: "cloudresourcemanager.googleapis.com/Folder"
    }
  ];
}

// A status object which is used as the `metadata` field for the `Operation`
// returned by `UndeleteFolder`.
message UndeleteFolderMetadata {}
