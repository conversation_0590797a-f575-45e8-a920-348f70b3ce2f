type: google.api.Service
config_version: 3
name: cloudtasks.googleapis.com
title: Cloud Tasks API

apis:
- name: google.cloud.location.Locations
- name: google.cloud.tasks.v2beta2.CloudTasks

documentation:
  summary: Manages the execution of large numbers of distributed requests.
  overview: |-
    Cloud Tasks manages the execution of large numbers of distributed
    requests.

    For more information, see https://cloud.google.com/tasks/.
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    description: Gets information about a location.

  - selector: google.cloud.location.Locations.ListLocations
    description: Lists information about the supported locations for this service.

backend:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    deadline: 5.0
  - selector: google.cloud.location.Locations.ListLocations
    deadline: 5.0
  - selector: 'google.cloud.tasks.v2beta2.CloudTasks.*'
    deadline: 15.0
  - selector: google.cloud.tasks.v2beta2.CloudTasks.UploadQueueYaml
    deadline: 300.0

http:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    get: '/v2beta2/{name=projects/*/locations/*}'
  - selector: google.cloud.location.Locations.ListLocations
    get: '/v2beta2/{name=projects/*}/locations'

authentication:
  rules:
  - selector: google.cloud.location.Locations.GetLocation
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: google.cloud.location.Locations.ListLocations
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
  - selector: 'google.cloud.tasks.v2beta2.CloudTasks.*'
    oauth:
      canonical_scopes: |-
        https://www.googleapis.com/auth/cloud-platform
