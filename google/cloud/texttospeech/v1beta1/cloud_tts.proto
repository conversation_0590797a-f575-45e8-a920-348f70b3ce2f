// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.cloud.texttospeech.v1beta1;

import "google/api/annotations.proto";
import "google/api/client.proto";
import "google/api/field_behavior.proto";
import "google/api/resource.proto";

option csharp_namespace = "Google.Cloud.TextToSpeech.V1Beta1";
option go_package = "cloud.google.com/go/texttospeech/apiv1beta1/texttospeechpb;texttospeechpb";
option java_multiple_files = true;
option java_outer_classname = "TextToSpeechProto";
option java_package = "com.google.cloud.texttospeech.v1beta1";
option objc_class_prefix = "CTTS";
option php_namespace = "Google\\Cloud\\TextToSpeech\\V1beta1";
option ruby_package = "Google::Cloud::TextToSpeech::V1beta1";
option (google.api.resource_definition) = {
  type: "automl.googleapis.com/Model"
  pattern: "projects/{project}/locations/{location}/models/{model}"
};

// Service that implements Google Cloud Text-to-Speech API.
service TextToSpeech {
  option (google.api.default_host) = "texttospeech.googleapis.com";
  option (google.api.oauth_scopes) =
      "https://www.googleapis.com/auth/cloud-platform";

  // Returns a list of Voice supported for synthesis.
  rpc ListVoices(ListVoicesRequest) returns (ListVoicesResponse) {
    option (google.api.http) = {
      get: "/v1beta1/voices"
    };
    option (google.api.method_signature) = "language_code";
  }

  // Synthesizes speech synchronously: receive results after all text input
  // has been processed.
  rpc SynthesizeSpeech(SynthesizeSpeechRequest)
      returns (SynthesizeSpeechResponse) {
    option (google.api.http) = {
      post: "/v1beta1/text:synthesize"
      body: "*"
    };
    option (google.api.method_signature) = "input,voice,audio_config";
  }

  // Performs bidirectional streaming speech synthesis: receive audio while
  // sending text.
  rpc StreamingSynthesize(stream StreamingSynthesizeRequest)
      returns (stream StreamingSynthesizeResponse) {}
}

// Gender of the voice as described in
// [SSML voice element](https://www.w3.org/TR/speech-synthesis11/#edef_voice).
enum SsmlVoiceGender {
  // An unspecified gender.
  // In VoiceSelectionParams, this means that the client doesn't care which
  // gender the selected voice will have. In the Voice field of
  // ListVoicesResponse, this may mean that the voice doesn't fit any of the
  // other categories in this enum, or that the gender of the voice isn't known.
  SSML_VOICE_GENDER_UNSPECIFIED = 0;

  // A male voice.
  MALE = 1;

  // A female voice.
  FEMALE = 2;

  // A gender-neutral voice. This voice is not yet supported.
  NEUTRAL = 3;
}

// Configuration to set up audio encoder. The encoding determines the output
// audio format that we'd like.
enum AudioEncoding {
  // Not specified. Will return result
  // [google.rpc.Code.INVALID_ARGUMENT][google.rpc.Code.INVALID_ARGUMENT].
  AUDIO_ENCODING_UNSPECIFIED = 0;

  // Uncompressed 16-bit signed little-endian samples (Linear PCM).
  // Audio content returned as LINEAR16 also contains a WAV header.
  LINEAR16 = 1;

  // MP3 audio at 32kbps.
  MP3 = 2;

  // MP3 at 64kbps.
  MP3_64_KBPS = 4;

  // Opus encoded audio wrapped in an ogg container. The result will be a
  // file which can be played natively on Android, and in browsers (at least
  // Chrome and Firefox). The quality of the encoding is considerably higher
  // than MP3 while using approximately the same bitrate.
  OGG_OPUS = 3;

  // 8-bit samples that compand 14-bit audio samples using G.711 PCMU/mu-law.
  // Audio content returned as MULAW also contains a WAV header.
  MULAW = 5;

  // 8-bit samples that compand 14-bit audio samples using G.711 PCMU/A-law.
  // Audio content returned as ALAW also contains a WAV header.
  ALAW = 6;

  // Uncompressed 16-bit signed little-endian samples (Linear PCM).
  // Note that as opposed to LINEAR16, audio will not be wrapped in a WAV (or
  // any other) header.
  PCM = 7;
}

// The top-level message sent by the client for the `ListVoices` method.
message ListVoicesRequest {
  // Optional. Recommended.
  // [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt) language tag.
  // If not specified, the API will return all supported voices.
  // If specified, the ListVoices call will only return voices that can be used
  // to synthesize this language_code. For example, if you specify `"en-NZ"`,
  // all `"en-NZ"` voices will be returned. If you specify `"no"`, both
  // `"no-\*"` (Norwegian) and `"nb-\*"` (Norwegian Bokmal) voices will be
  // returned.
  string language_code = 1 [(google.api.field_behavior) = OPTIONAL];
}

// The message returned to the client by the `ListVoices` method.
message ListVoicesResponse {
  // The list of voices.
  repeated Voice voices = 1;
}

// Description of a voice supported by the TTS service.
message Voice {
  // The languages that this voice supports, expressed as
  // [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt) language tags (e.g.
  // "en-US", "es-419", "cmn-tw").
  repeated string language_codes = 1;

  // The name of this voice.  Each distinct voice has a unique name.
  string name = 2;

  // The gender of this voice.
  SsmlVoiceGender ssml_gender = 3;

  // The natural sample rate (in hertz) for this voice.
  int32 natural_sample_rate_hertz = 4;
}

// Used for advanced voice options.
message AdvancedVoiceOptions {
  // Only for Journey voices. If false, the synthesis will be context aware
  // and have higher latency.
  optional bool low_latency_journey_synthesis = 1;
}

// The top-level message sent by the client for the `SynthesizeSpeech` method.
message SynthesizeSpeechRequest {
  // The type of timepoint information that is returned in the response.
  enum TimepointType {
    // Not specified. No timepoint information will be returned.
    TIMEPOINT_TYPE_UNSPECIFIED = 0;

    // Timepoint information of `<mark>` tags in SSML input will be returned.
    SSML_MARK = 1;
  }

  // Required. The Synthesizer requires either plain text or SSML as input.
  SynthesisInput input = 1 [(google.api.field_behavior) = REQUIRED];

  // Required. The desired voice of the synthesized audio.
  VoiceSelectionParams voice = 2 [(google.api.field_behavior) = REQUIRED];

  // Required. The configuration of the synthesized audio.
  AudioConfig audio_config = 3 [(google.api.field_behavior) = REQUIRED];

  // Whether and what timepoints are returned in the response.
  repeated TimepointType enable_time_pointing = 4;

  // Advanced voice options.
  optional AdvancedVoiceOptions advanced_voice_options = 8;
}

// Pronunciation customization for a phrase.
message CustomPronunciationParams {
  // The phonetic encoding of the phrase.
  enum PhoneticEncoding {
    // Not specified.
    PHONETIC_ENCODING_UNSPECIFIED = 0;

    // IPA. (e.g. apple -> ˈæpəl )
    // https://en.wikipedia.org/wiki/International_Phonetic_Alphabet
    PHONETIC_ENCODING_IPA = 1;

    // X-SAMPA (e.g. apple -> "{p@l" )
    // https://en.wikipedia.org/wiki/X-SAMPA
    PHONETIC_ENCODING_X_SAMPA = 2;
  }

  // The phrase to which the customization will be applied.
  // The phrase can be multiple words (in the case of proper nouns etc), but
  // should not span to a whole sentence.
  optional string phrase = 1;

  // The phonetic encoding of the phrase.
  optional PhoneticEncoding phonetic_encoding = 2;

  // The pronunciation of the phrase. This must be in the phonetic encoding
  // specified above.
  optional string pronunciation = 3;
}

// A collection of pronunciation customizations.
message CustomPronunciations {
  // The pronunciation customizations to be applied.
  repeated CustomPronunciationParams pronunciations = 1;
}

// A collection of turns for multi-speaker synthesis.
message MultiSpeakerMarkup {
  // A Multi-speaker turn.
  message Turn {
    // Required. The speaker of the turn, for example, 'O' or 'Q'. Please refer
    // to documentation for available speakers.
    string speaker = 1 [(google.api.field_behavior) = REQUIRED];

    // Required. The text to speak.
    string text = 2 [(google.api.field_behavior) = REQUIRED];
  }

  // Required. Speaker turns.
  repeated Turn turns = 1 [(google.api.field_behavior) = REQUIRED];
}

// Contains text input to be synthesized. Either `text` or `ssml` must be
// supplied. Supplying both or neither returns
// [google.rpc.Code.INVALID_ARGUMENT][google.rpc.Code.INVALID_ARGUMENT]. The
// input size is limited to 5000 bytes.
message SynthesisInput {
  // The input source, which is either plain text or SSML.
  oneof input_source {
    // The raw text to be synthesized.
    string text = 1;

    // The SSML document to be synthesized. The SSML document must be valid
    // and well-formed. Otherwise the RPC will fail and return
    // [google.rpc.Code.INVALID_ARGUMENT][google.rpc.Code.INVALID_ARGUMENT]. For
    // more information, see
    // [SSML](https://cloud.google.com/text-to-speech/docs/ssml).
    string ssml = 2;

    // The multi-speaker input to be synthesized. Only applicable for
    // multi-speaker synthesis.
    MultiSpeakerMarkup multi_speaker_markup = 4;
  }

  // Optional. The pronunciation customizations to be applied to the input. If
  // this is set, the input will be synthesized using the given pronunciation
  // customizations.
  //
  // The initial support will be for EFIGS (English, French,
  // Italian, German, Spanish) languages, as provided in
  // VoiceSelectionParams. Journey and Instant Clone voices are
  // not supported yet.
  //
  // In order to customize the pronunciation of a phrase, there must be an exact
  // match of the phrase in the input types. If using SSML, the phrase must not
  // be inside a phoneme tag (entirely or partially).
  CustomPronunciations custom_pronunciations = 3
      [(google.api.field_behavior) = OPTIONAL];
}

// Description of which voice to use for a synthesis request.
message VoiceSelectionParams {
  // Required. The language (and potentially also the region) of the voice
  // expressed as a [BCP-47](https://www.rfc-editor.org/rfc/bcp/bcp47.txt)
  // language tag, e.g. "en-US". This should not include a script tag (e.g. use
  // "cmn-cn" rather than "cmn-Hant-cn"), because the script will be inferred
  // from the input provided in the SynthesisInput.  The TTS service
  // will use this parameter to help choose an appropriate voice.  Note that
  // the TTS service may choose a voice with a slightly different language code
  // than the one selected; it may substitute a different region
  // (e.g. using en-US rather than en-CA if there isn't a Canadian voice
  // available), or even a different language, e.g. using "nb" (Norwegian
  // Bokmal) instead of "no" (Norwegian)".
  string language_code = 1 [(google.api.field_behavior) = REQUIRED];

  // The name of the voice. If both the name and the gender are not set,
  // the service will choose a voice based on the other parameters such as
  // language_code.
  string name = 2;

  // The preferred gender of the voice. If not set, the service will
  // choose a voice based on the other parameters such as language_code and
  // name. Note that this is only a preference, not requirement; if a
  // voice of the appropriate gender is not available, the synthesizer should
  // substitute a voice with a different gender rather than failing the request.
  SsmlVoiceGender ssml_gender = 3;

  // The configuration for a custom voice. If [CustomVoiceParams.model] is set,
  // the service will choose the custom voice matching the specified
  // configuration.
  CustomVoiceParams custom_voice = 4;

  // Optional. The configuration for a voice clone. If
  // [VoiceCloneParams.voice_clone_key] is set, the service will choose the
  // voice clone matching the specified configuration.
  VoiceCloneParams voice_clone = 5 [(google.api.field_behavior) = OPTIONAL];
}

// Description of audio data to be synthesized.
message AudioConfig {
  // Required. The format of the audio byte stream.
  AudioEncoding audio_encoding = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. Input only. Speaking rate/speed, in the range [0.25, 4.0]. 1.0 is
  // the normal native speed supported by the specific voice. 2.0 is twice as
  // fast, and 0.5 is half as fast. If unset(0.0), defaults to the native 1.0
  // speed. Any other values < 0.25 or > 4.0 will return an error.
  double speaking_rate = 2 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Input only. Speaking pitch, in the range [-20.0, 20.0]. 20 means
  // increase 20 semitones from the original pitch. -20 means decrease 20
  // semitones from the original pitch.
  double pitch = 3 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. Input only. Volume gain (in dB) of the normal native volume
  // supported by the specific voice, in the range [-96.0, 16.0]. If unset, or
  // set to a value of 0.0 (dB), will play at normal native signal amplitude. A
  // value of -6.0 (dB) will play at approximately half the amplitude of the
  // normal native signal amplitude. A value of **** (dB) will play at
  // approximately twice the amplitude of the normal native signal amplitude.
  // Strongly recommend not to exceed +10 (dB) as there's usually no effective
  // increase in loudness for any value greater than that.
  double volume_gain_db = 4 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];

  // Optional. The synthesis sample rate (in hertz) for this audio. When this is
  // specified in SynthesizeSpeechRequest, if this is different from the voice's
  // natural sample rate, then the synthesizer will honor this request by
  // converting to the desired sample rate (which might result in worse audio
  // quality), unless the specified sample rate is not supported for the
  // encoding chosen, in which case it will fail the request and return
  // [google.rpc.Code.INVALID_ARGUMENT][google.rpc.Code.INVALID_ARGUMENT].
  int32 sample_rate_hertz = 5 [(google.api.field_behavior) = OPTIONAL];

  // Optional. Input only. An identifier which selects 'audio effects' profiles
  // that are applied on (post synthesized) text to speech. Effects are applied
  // on top of each other in the order they are given. See
  // [audio
  // profiles](https://cloud.google.com/text-to-speech/docs/audio-profiles) for
  // current supported profile ids.
  repeated string effects_profile_id = 6 [
    (google.api.field_behavior) = INPUT_ONLY,
    (google.api.field_behavior) = OPTIONAL
  ];
}

// Description of the custom voice to be synthesized.
message CustomVoiceParams {
  // Deprecated. The usage of the synthesized audio. Usage does not affect
  // billing.
  enum ReportedUsage {
    // Request with reported usage unspecified will be rejected.
    REPORTED_USAGE_UNSPECIFIED = 0;

    // For scenarios where the synthesized audio is not downloadable and can
    // only be used once. For example, real-time request in IVR system.
    REALTIME = 1;

    // For scenarios where the synthesized audio is downloadable and can be
    // reused. For example, the synthesized audio is downloaded, stored in
    // customer service system and played repeatedly.
    OFFLINE = 2;
  }

  // Required. The name of the AutoML model that synthesizes the custom voice.
  string model = 1 [
    (google.api.field_behavior) = REQUIRED,
    (google.api.resource_reference) = { type: "automl.googleapis.com/Model" }
  ];

  // Optional. Deprecated. The usage of the synthesized audio to be reported.
  ReportedUsage reported_usage = 3
      [deprecated = true, (google.api.field_behavior) = OPTIONAL];
}

// The configuration of Voice Clone feature.
message VoiceCloneParams {
  // Required. Created by GenerateVoiceCloningKey.
  string voice_cloning_key = 1 [(google.api.field_behavior) = REQUIRED];
}

// The message returned to the client by the `SynthesizeSpeech` method.
message SynthesizeSpeechResponse {
  // The audio data bytes encoded as specified in the request, including the
  // header for encodings that are wrapped in containers (e.g. MP3, OGG_OPUS).
  // For LINEAR16 audio, we include the WAV header. Note: as
  // with all bytes fields, protobuffers use a pure binary representation,
  // whereas JSON representations use base64.
  bytes audio_content = 1;

  // A link between a position in the original request input and a corresponding
  // time in the output audio. It's only supported via `<mark>` of SSML input.
  repeated Timepoint timepoints = 2;

  // The audio metadata of `audio_content`.
  AudioConfig audio_config = 4;
}

// This contains a mapping between a certain point in the input text and a
// corresponding time in the output audio.
message Timepoint {
  // Timepoint name as received from the client within `<mark>` tag.
  string mark_name = 4;

  // Time offset in seconds from the start of the synthesized audio.
  double time_seconds = 3;
}

// Description of the desired output audio data.
message StreamingAudioConfig {
  // Required. The format of the audio byte stream.
  // For now, streaming only supports PCM and OGG_OPUS. All other encodings
  // will return an error.
  AudioEncoding audio_encoding = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The synthesis sample rate (in hertz) for this audio.
  int32 sample_rate_hertz = 2 [(google.api.field_behavior) = OPTIONAL];
}

// Provides configuration information for the StreamingSynthesize request.
message StreamingSynthesizeConfig {
  // Required. The desired voice of the synthesized audio.
  VoiceSelectionParams voice = 1 [(google.api.field_behavior) = REQUIRED];

  // Optional. The configuration of the synthesized audio.
  StreamingAudioConfig streaming_audio_config = 4
      [(google.api.field_behavior) = OPTIONAL];
}

// Input to be synthesized.
message StreamingSynthesisInput {
  oneof input_source {
    // The raw text to be synthesized. It is recommended that each input
    // contains complete, terminating sentences, as this will likely result in
    // better prosody in the output audio. That being said, users are free to
    // input text however they please.
    string text = 1;
  }
}

// Request message for the `StreamingSynthesize` method. Multiple
// `StreamingSynthesizeRequest` messages are sent in one call.
// The first message must contain a `streaming_config` that
// fully specifies the request configuration and must not contain `input`. All
// subsequent messages must only have `input` set.
message StreamingSynthesizeRequest {
  // The request to be sent, either a StreamingSynthesizeConfig or
  // StreamingSynthesisInput.
  oneof streaming_request {
    // StreamingSynthesizeConfig to be used in this streaming attempt. Only
    // specified in the first message sent in a `StreamingSynthesize` call.
    StreamingSynthesizeConfig streaming_config = 1;

    // Input to synthesize. Specified in all messages but the first in a
    // `StreamingSynthesize` call.
    StreamingSynthesisInput input = 2;
  }
}

// `StreamingSynthesizeResponse` is the only message returned to the
// client by `StreamingSynthesize` method. A series of zero or more
// `StreamingSynthesizeResponse` messages are streamed back to the client.
message StreamingSynthesizeResponse {
  // The audio data bytes encoded as specified in the request. This is
  // headerless LINEAR16 audio with a sample rate of 24000.
  bytes audio_content = 1;
}
