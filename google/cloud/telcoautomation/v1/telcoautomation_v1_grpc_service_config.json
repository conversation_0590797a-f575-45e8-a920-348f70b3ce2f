{"methodConfig": [{"name": [{"service": "google.cloud.library.v1.LibraryService", "method": "ListOrchestrationClusters"}, {"service": "google.cloud.library.v1.LibraryService", "method": "GetOrchestrationCluster"}, {"service": "google.cloud.library.v1.LibraryService", "method": "GetBlueprint"}, {"service": "google.cloud.library.v1.LibraryService", "method": "ListBlueprints"}, {"service": "google.cloud.library.v1.LibraryService", "method": "ListBlueprintRevisions"}, {"service": "google.cloud.library.v1.LibraryService", "method": "ListDeploymentRevisions"}, {"service": "google.cloud.library.v1.LibraryService", "method": "SearchBlueprintRevisions"}, {"service": "google.cloud.library.v1.LibraryService", "method": "SearchDeploymentRevisions"}, {"service": "google.cloud.library.v1.LibraryService", "method": "ListPublicBlueprints"}, {"service": "google.cloud.library.v1.LibraryService", "method": "GetPublicBlueprint"}, {"service": "google.cloud.library.v1.LibraryService", "method": "GetDeployment"}, {"service": "google.cloud.library.v1.LibraryService", "method": "ListDeployments"}, {"service": "google.cloud.library.v1.LibraryService", "method": "ComputeDeploymentStatus"}, {"service": "google.cloud.library.v1.LibraryService", "method": "GetHydratedDeployment"}, {"service": "google.cloud.library.v1.LibraryService", "method": "ListHydratedDeployments"}], "timeout": "60s", "retryPolicy": {"maxAttempts": 5, "initialBackoff": "1s", "maxBackoff": "10s", "backoffMultiplier": 1.3, "retryableStatusCodes": ["UNAVAILABLE"]}}, {"name": [{"service": "google.cloud.library.v1.LibraryService", "method": "CreateOrchestrationCluster"}, {"service": "google.cloud.library.v1.LibraryService", "method": "DeleteOrchestrationCluster"}, {"service": "google.cloud.library.v1.LibraryService", "method": "CreateBlueprint"}, {"service": "google.cloud.library.v1.LibraryService", "method": "UpdateBlueprint"}, {"service": "google.cloud.library.v1.LibraryService", "method": "DeleteBlueprint"}, {"service": "google.cloud.library.v1.LibraryService", "method": "ApproveBlueprint"}, {"service": "google.cloud.library.v1.LibraryService", "method": "ProposeBlueprint"}, {"service": "google.cloud.library.v1.LibraryService", "method": "RejectBlueprint"}, {"service": "google.cloud.library.v1.LibraryService", "method": "DiscardBlueprintChanges"}, {"service": "google.cloud.library.v1.LibraryService", "method": "CreateDeployment"}, {"service": "google.cloud.library.v1.LibraryService", "method": "UpdateDeployment"}, {"service": "google.cloud.library.v1.LibraryService", "method": "RemoveDeployment"}, {"service": "google.cloud.library.v1.LibraryService", "method": "ApplyDeployment"}, {"service": "google.cloud.library.v1.LibraryService", "method": "RollbackDeployment"}, {"service": "google.cloud.library.v1.LibraryService", "method": "UpdateHydratedDeployment"}, {"service": "google.cloud.library.v1.LibraryService", "method": "ApplyHydratedDeployment"}], "timeout": "60s"}]}