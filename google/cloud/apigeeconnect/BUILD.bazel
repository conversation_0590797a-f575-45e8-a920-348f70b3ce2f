# This build file includes a target for the Ruby wrapper library for
# google-cloud-apigee_connect.

# This is an API workspace, having public visibility by default makes perfect sense.
package(default_visibility = ["//visibility:public"])

# Export yaml configs.
exports_files(glob(["*.yaml"]))

load(
    "@com_google_googleapis_imports//:imports.bzl",
    "ruby_cloud_gapic_library",
    "ruby_gapic_assembly_pkg",
)

# Generates a Ruby wrapper client for apigeeconnect.
# Ruby wrapper clients are versionless, but are generated from source protos
# for a particular service version, v1 in this case.
ruby_cloud_gapic_library(
    name = "apigeeconnect_ruby_wrapper",
    srcs = ["//google/cloud/apigeeconnect/v1:apigeeconnect_proto_with_info"],
    extra_protoc_parameters = [
        "ruby-cloud-gem-name=google-cloud-apigee_connect",
        "ruby-cloud-env-prefix=APIGEE_CONNECT",
        "ruby-cloud-wrapper-of=v1:0.6",
        "ruby-cloud-product-url=https://cloud.google.com/apigee/docs/hybrid/v1.4/apigee-connect",
        "ruby-cloud-api-id=apigeeconnect.googleapis.com",
        "ruby-cloud-api-shortname=apigeeconnect",
    ],
    ruby_cloud_description = "Apigee Connect allows the Apigee hybrid management plane to connect securely to the MART service in the runtime plane without requiring you to expose the MART endpoint on the internet. If you use Apigee Connect, you do not need to configure the MART ingress gateway with a host alias and an authorized DNS certificate.",
    ruby_cloud_title = "Apigee Connect",
    transport = "grpc+rest",
)

# Open Source package.
ruby_gapic_assembly_pkg(
    name = "google-cloud-apigeeconnect-ruby",
    deps = [
        ":apigeeconnect_ruby_wrapper",
    ],
)
