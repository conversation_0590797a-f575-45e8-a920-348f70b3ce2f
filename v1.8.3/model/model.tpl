package {{.pkg}}

import (
    "context"

    {{if .containsPQ}}"github.com/lib/pq"{{end}}
    "github.com/Masterminds/squirrel"
    {{if .withCache}}"github.com/zeromicro/go-zero/core/stores/cache"{{end}}
    "github.com/zeromicro/go-zero/core/stores/sqlx"
    "github.com/zeromicro/go-zero/core/stringx"

    "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/constants"
    "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/types"
)

var (
    _ {{.upperStartCamelObject}}Model = (*custom{{.upperStartCamelObject}}Model)(nil)

    {{.lowerStartCamelObject}}InsertFields = stringx.Remove({{.lowerStartCamelObject}}FieldNames, "`id`", "`create_time`", "`update_time`", "`deleted_by`", "`created_at`", "`updated_at`", "`deleted_at`")
)

type (
	// {{.upperStartCamelObject}}Model is an interface to be customized, add more methods here,
	// and implement the added methods in custom{{.upperStartCamelObject}}Model.
	{{.upperStartCamelObject}}Model interface {
		{{.lowerStartCamelObject}}Model
		types.DBModel

		{{if not .withCache}}withSession(session sqlx.Session) {{.upperStartCamelObject}}Model{{end}}
		
		Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error
		InsertBuilder(data *{{.upperStartCamelObject}}) squirrel.InsertBuilder
		UpdateBuilder(data *{{.upperStartCamelObject}}) squirrel.UpdateBuilder
		SelectBuilder() squirrel.SelectBuilder
		SelectCountBuilder() squirrel.SelectBuilder
		FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error)
		FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*{{.upperStartCamelObject}}, error)
	}

	custom{{.upperStartCamelObject}}Model struct {
		*default{{.upperStartCamelObject}}Model
		
		conn sqlx.SqlConn
	}
)

// New{{.upperStartCamelObject}}Model returns a model for the database table.
func New{{.upperStartCamelObject}}Model(conn sqlx.SqlConn{{if .withCache}}, c cache.CacheConf, opts ...cache.Option{{end}}) {{.upperStartCamelObject}}Model {
	return &custom{{.upperStartCamelObject}}Model{
		default{{.upperStartCamelObject}}Model: new{{.upperStartCamelObject}}Model(conn{{if .withCache}}, c, opts...{{end}}),
		conn: conn,
	}
}

{{if not .withCache}}
func (m *custom{{.upperStartCamelObject}}Model) withSession(session sqlx.Session) {{.upperStartCamelObject}}Model {
    return New{{.upperStartCamelObject}}Model(sqlx.NewSqlConnFromSession(session))
}
{{end}}

func (m *custom{{.upperStartCamelObject}}Model) Table() string {
	return m.table
}

func (m *custom{{.upperStartCamelObject}}Model) Fields() []string {
	return {{.lowerStartCamelObject}}FieldNames
}

func (m *custom{{.upperStartCamelObject}}Model) Trans(ctx context.Context, fn func(context context.Context, session sqlx.Session) error) error {
	return {{if .withCache}}m.TransactCtx(ctx, fn){{else}}m.conn.TransactCtx(ctx, fn){{end}}
}

func (m *custom{{.upperStartCamelObject}}Model) InsertBuilder(data *{{.upperStartCamelObject}}) squirrel.InsertBuilder {
    // TODO: fill the insert values
    return squirrel.Insert(m.table).Columns({{.lowerStartCamelObject}}InsertFields...).Values()
}

func (m *custom{{.upperStartCamelObject}}Model) UpdateBuilder(data *{{.upperStartCamelObject}}) squirrel.UpdateBuilder {
	eq := squirrel.Eq{
	    // TODO: fill the update values
	}
	return squirrel.Update(m.table).SetMap(eq).Where("`id` = ?", data.Id)
}

func (m *custom{{.upperStartCamelObject}}Model) SelectBuilder() squirrel.SelectBuilder {
	return squirrel.Select({{.lowerStartCamelObject}}FieldNames...).Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *custom{{.upperStartCamelObject}}Model) SelectCountBuilder() squirrel.SelectBuilder {
	return squirrel.Select("count(*)").Where("`deleted` = ?", constants.NotDeleted).From(m.table)
}

func (m *custom{{.upperStartCamelObject}}Model) FindCount(ctx context.Context, countBuilder squirrel.SelectBuilder) (int64, error) {
	query, values, err := countBuilder.ToSql()
	if err != nil {
		return 0, err
	}

	var resp int64
	err = {{if .withCache}}m.QueryRowNoCacheCtx{{else}}m.conn.QueryRowCtx{{end}}(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return 0, err
	}
}

func (m *custom{{.upperStartCamelObject}}Model) FindNoCacheByQuery(ctx context.Context, selectBuilder squirrel.SelectBuilder) ([]*{{.upperStartCamelObject}}, error) {
	query, values, err := selectBuilder.ToSql()
	if err != nil {
		return nil, err
	}

	var resp []*{{.upperStartCamelObject}}
	err = {{if .withCache}}m.QueryRowsNoCacheCtx{{else}}m.conn.QueryRowsCtx{{end}}(ctx, &resp, query, values...)
	switch err {
	case nil:
		return resp, nil
	default:
		return nil, err
	}
}
