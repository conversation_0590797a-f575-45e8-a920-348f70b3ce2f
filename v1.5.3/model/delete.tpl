func (m *default{{.upperStartCamelObject}}Model) Delete(ctx context.Context, session sqlx.Session, {{.lowerStartCamelPrimaryKey}} {{.dataType}}) error {
	{{if .withCache}}{{if .containsIndexCache}}data, err := m.<PERSON>ne(ctx, {{.lowerStartCamelPrimaryKey}})
	if err != nil {
		return err
	}

	{{end}}	{{.keys}}
	_, err {{if .containsIndexCache}}={{else}}:={{end}} m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
	    query := fmt.Sprintf("delete from %s where {{.originalPrimaryKey}} = {{if .postgreSql}}$1{{else}}?{{end}}", m.table)
	    if session!=nil{
		    return session.ExecCtx(ctx, query, {{.lowerStartCamelPrimaryKey}})
	    }
	    return conn.ExecCtx(ctx, query, {{.lowerStartCamelPrimaryKey}})
	}, {{.keyValues}}){{else}}query := fmt.Sprintf("delete from %s where {{.originalPrimaryKey}} = {{if .postgreSql}}$1{{else}}?{{end}}", m.table)
	if session != nil {
	    _, err := session.ExecCtx(ctx, query, {{.lowerStartCamelPrimaryKey}})
		return err
	}
	_, err := m.conn.ExecCtx(ctx, query, {{.lowerStartCamelPrimaryKey}}){{end}}
	return err
}

func (m *default{{.upperStartCamelObject}}Model) LogicDelete(ctx context.Context, session sqlx.Session, {{.lowerStartCamelPrimaryKey}} {{.dataType}}) error {
    {{if .withCache}}{{if .containsIndexCache}}data, err := m.FindOne(ctx, {{.lowerStartCamelPrimaryKey}})
	if err != nil {
		return err
	}

    {{end}}	{{.keys}}
	_, err {{if .containsIndexCache}}={{else}}:={{end}} m.ExecCtx(ctx, func(ctx context.Context, conn sqlx.SqlConn) (result sql.Result, err error) {
		query := fmt.Sprintf("update %s set {{if .postgreSql}}deleted = $1, deleted_at = $2{{else}}`deleted` = ?, `deleted_at` = ?{{end}} where {{.originalPrimaryKey}} = {{if .postgreSql}}$3{{else}}?{{end}}", m.table)
		if session != nil {
			return session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, {{.lowerStartCamelPrimaryKey}})
		}
		return conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, {{.lowerStartCamelPrimaryKey}})
	}, {{.keyValues}}){{else}}
	query := fmt.Sprintf("update %s set {{if .postgreSql}}deleted = $1, deleted_at = $2{{else}}`deleted` = ?, `deleted_at` = ?{{end}} where {{.originalPrimaryKey}} = {{if .postgreSql}}$3{{else}}?{{end}}", m.table)
    if session != nil {
        _, err := session.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, {{.lowerStartCamelPrimaryKey}})
    	return err
    }
    _, err := m.conn.ExecCtx(ctx, query, constants.HasDeleted, sql.NullTime{Time: time.Now(), Valid: true}, {{.lowerStartCamelPrimaryKey}}){{end}}
	return err
}
