package clickPilot

import (
	"fmt"
	"net/http"
	"net/url"

	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/transport/http/fasthttp"
)

var headerOfApplicationJSONContentType = http.Header{"Content-Type": {"application/json"}}

// Client ClickPilot客户端
type Client struct {
	c    *fasthttp.Client
	conf Config
}

// NewClient 创建ClickPilot客户端
func NewClient(conf Config) *Client {
	return &Client{
		c: fasthttp.NewClient(
			fasthttp.ClientConf{
				BaseURL: conf.BaseURL,
			},
		),
		conf: conf,
	}
}

func (c *Client) send(apiName string, req *fasthttp.Request, resp *fasthttp.Response) (
	body []byte, err error,
) {
	if err = c.c.Send(req, resp, requestTimeout); err != nil {
		return body, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of ClickPilot, api: %s, error: %+v",
			apiName, err,
		)
	}

	body = resp.Body()
	status := resp.StatusCode()
	if status != http.StatusOK {
		return body, errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to call the api of ClickPilot, api: %s, status code: %d, resp body: %s",
			apiName, status, body,
		)
	}

	return body, err
}

// CreateUITask 创建UI任务
func (c *Client) CreateUITask(in *createUITaskReq) (*createUITaskResp, error) {
	apiName := createUITaskAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodPost),
		fasthttp.SetHeader(headerOfApplicationJSONContentType.Clone()),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(in)),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out createUITaskResp
	if err := jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return &out, nil
}

// GetTaskStatus 查询任务状态
func (c *Client) GetTaskStatus(taskID string) (TaskStatus, error) {
	apiName := getTaskStatusAPIName

	values := url.Values{}
	values.Add(queryOfTaskID, taskID)

	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(fmt.Sprintf("%s?%s", apiName, values.Encode()))),
		fasthttp.SetMethod(http.MethodGet),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return TaskStatusOfNull, err
	}

	var out getTaskStatusResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return TaskStatusOfNull, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return out.Data.Status, nil
}

// GetTaskRecord 查询任务执行步骤记录
func (c *Client) GetTaskRecord(taskID string) (*getTaskRecordResp, error) {
	apiName := getTaskRecordAPIName

	values := url.Values{}
	values.Add(queryOfTaskID, taskID)

	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(fmt.Sprintf("%s?%s", apiName, values.Encode()))),
		fasthttp.SetMethod(http.MethodGet),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return nil, err
	}

	var out getTaskRecordResp
	if err := jsonx.Unmarshal(body, &out); err != nil {
		return nil, errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	return &out, nil
}

// GetTaskLog 查询任务日志
func (c *Client) GetTaskLog(taskID string) (string, error) {
	apiName := getTaskLogAPIName

	values := url.Values{}
	values.Add(queryOfTaskID, taskID)

	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(fmt.Sprintf("%s?%s", apiName, values.Encode()))),
		fasthttp.SetMethod(http.MethodGet),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return "", err
	}

	var out getTaskLogResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return "", errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return "", errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to get task log, api: %s, code: %d, message: %s",
			apiName, out.Code, out.Message,
		)
	}

	return out.Data, nil
}

// StopTask 停止任务
func (c *Client) StopTask(taskID string) error {
	apiName := stopTaskAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodPost),
		fasthttp.SetHeader(headerOfApplicationJSONContentType.Clone()),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(&stopTaskReq{TaskID: taskID})),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return err
	}

	var out stopTaskResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to stop task, api: %s, code: %d, message: %s",
			apiName, out.Code, out.Message,
		)
	} else if !out.Data.Success {
		return errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to stop task, api: %s, message: %s",
			apiName, out.Data.Message,
		)
	}

	return nil
}

// DeleteTask 删除任务
func (c *Client) DeleteTask(taskID string) error {
	apiName := deleteTaskAPIName
	req := fasthttp.NewRequest(
		fasthttp.SetURL(c.c.BuildURL(apiName)),
		fasthttp.SetMethod(http.MethodDelete),
		fasthttp.SetHeader(headerOfApplicationJSONContentType.Clone()),
		fasthttp.SetBody(jsonx.MarshalIgnoreError(&deleteTaskReq{TaskID: taskID})),
	)
	defer fasthttp.ReleaseRequest(req)

	resp := fasthttp.AcquireResponse()
	defer fasthttp.ReleaseResponse(resp)

	body, err := c.send(apiName, req, resp)
	if err != nil {
		return err
	}

	var out deleteTaskResp
	if err = jsonx.Unmarshal(body, &out); err != nil {
		return errorx.Errorf(
			errorx.SerializationError,
			"failed to unmarshal the response of ClickPilot, api: %s, resp body: %s, error: %+v",
			apiName, body, err,
		)
	}

	if out.Code != 0 {
		return errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to delete task, api: %s, code: %d, message: %s",
			apiName, out.Code, out.Message,
		)
	} else if !out.Data.Success {
		return errorx.Errorf(
			errorx.CallExternalAPIFailure,
			"failed to delete task, api: %s, message: %s",
			apiName, out.Data.Message,
		)
	}

	return nil
}
