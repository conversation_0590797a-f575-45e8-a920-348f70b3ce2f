package clickPilot

// Config ClickPilot客户端配置
type Config struct {
	BaseURL string `json:",default=http://localhost:8080"` // ClickPilot服务地址
}

// TaskExpectResult 任务期望结果
type TaskExpectResult struct {
	Text  string `json:"text,omitempty"`  // 期望结果文字描述
	Image string `json:"image,omitempty"` // 期望结果图片路径（可选）
}

// TaskStep 任务步骤
type TaskStep struct {
	Step         string            `json:"step"`          // 步骤名称
	ExpectResult *TaskExpectResult `json:"expect_result"` // 每一步期望结果（可选）
}

// Device 设备配置
type Device struct {
	Type    DeviceType     `json:"type"`              // android / ios
	Android *AndroidDevice `json:"android,omitempty"` // Android设备配置
	IOS     *IOSDevice     `json:"ios,omitempty"`     // iOS设备配置
}

// AndroidDevice Android设备配置
type AndroidDevice struct {
	URL string `json:"url"` // adb连接地址
}

// IOSDevice iOS设备配置
type IOSDevice struct {
	URL string `json:"url"` // iOS设备连接地址
}

type BaseResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type (
	// createUITaskReq 创建UI任务请求
	createUITaskReq struct {
		TaskID                  string            `json:"task_id,omitempty"`                   // 任务ID
		TaskName                string            `json:"task_name"`                           // 测试用例名称
		TaskExpectResult        *TaskExpectResult `json:"task_expect_result"`                  // 整个用例期望结果
		TaskStepByStep          []*TaskStep       `json:"task_step_by_step"`                   // 分步骤验证模式（可选）
		TaskAggregationStep     string            `json:"task_aggregation_step"`               // 分步骤聚合模式（可选）
		AgentType               AgentType         `json:"agent_type"`                          // android / ios，默认android
		AgentConfigID           string            `json:"agent_config_id"`                     // agent的开放Prompt，默认值'tt'
		Device                  *Device           `json:"device"`                              // 设备配置
		AppID                   string            `json:"app_id"`                              // 执行的软件app_id
		AppName                 string            `json:"app_name,omitempty"`                  // 应用名称
		AppDescription          string            `json:"app_description,omitempty"`           // 软件功能的介绍
		UIComponentInstructions string            `json:"ui_component_instructions,omitempty"` // UI组件操作说明
		SpecialScenarios        string            `json:"special_scenarios,omitempty"`         // 特殊场景处理说明
		Restart                 bool              `json:"restart,omitempty"`                   // 是否重启app
	}

	// createUITaskRespData 创建UI任务响应数据
	createUITaskRespData struct {
		TaskID string `json:"task_id"`
	}

	// createUITaskResp 创建UI任务响应
	createUITaskResp struct {
		BaseResp

		Data *createUITaskRespData `json:"data"`
	}
)

// 查询任务状态
type (
	// getTaskStatusRespData 查询任务状态响应数据
	getTaskStatusRespData struct {
		Status  TaskStatus `json:"status"`  // 任务状态
		Message string     `json:"message"` // 状态描述
	}

	// getTaskStatusResp 查询任务状态响应
	getTaskStatusResp struct {
		BaseResp

		Data getTaskStatusRespData `json:"data"`
	}
)

// 查询任务执行步骤记录
type (
	// getTaskRecordRespData 查询任务执行步骤记录响应数据
	getTaskRecordRespData struct {
		Number    int        `json:"number"`     // 步骤序号
		StepName  string     `json:"step_name"`  // 步骤名称
		Thought   string     `json:"thought"`    // 决策思考过程
		Cost      float64    `json:"cost"`       // 执行耗时（秒）
		ImagePath string     `json:"image_path"` // 截图路径
		Action    string     `json:"action"`     // 执行的动作
		Status    StepStatus `json:"status"`     // 步骤状态
		StartTime string     `json:"start_time"` // 开始时间
		EndTime   string     `json:"end_time"`   // 结束时间
	}

	// getTaskRecordResp 查询任务执行步骤记录响应
	getTaskRecordResp struct {
		BaseResp

		Data []*getTaskRecordRespData `json:"data"`
	}
)

// 查询任务日志
type (
	// getTaskLogResp 查询任务日志响应
	getTaskLogResp struct {
		BaseResp

		Data string `json:"data"` // 日志内容
	}
)

// 停止任务
type (
	// stopTaskReq 停止任务请求
	stopTaskReq struct {
		TaskID string `json:"task_id"`
	}

	// stopTaskRespData 停止任务响应数据
	stopTaskRespData struct {
		Success bool   `json:"success"`
		TaskID  string `json:"task_id"`
		Message string `json:"message"`
	}

	// stopTaskResp 停止任务响应
	stopTaskResp struct {
		BaseResp

		Data stopTaskRespData `json:"data"`
	}
)

// 删除任务
type (
	// deleteTaskReq 删除任务请求
	deleteTaskReq struct {
		TaskID string `json:"task_id"`
	}

	// deleteTaskRespData 删除任务响应数据
	deleteTaskRespData struct {
		Success bool   `json:"success"`
		TaskID  string `json:"task_id"`
		Message string `json:"message"`
	}

	// deleteTaskResp 删除任务响应
	deleteTaskResp struct {
		BaseResp

		Data deleteTaskRespData `json:"data"`
	}
)
