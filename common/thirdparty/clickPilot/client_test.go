package clickPilot

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestNewClient(t *testing.T) {
	conf := Config{
		BaseURL: "http://localhost:8080",
	}

	client := NewClient(conf)
	assert.NotNil(t, client)
	assert.Equal(t, conf.BaseURL, client.conf.BaseURL)
}

func TestCreateUITaskReq_Structure(t *testing.T) {
	// 测试请求结构体是否正确定义
	req := &createUITaskReq{
		TaskName: "修改生日",
		TaskExpectResult: &TaskExpectResult{
			Text:  "生日修改成功",
			Image: "image_path",
		},
		TaskStepByStep: []*TaskStep{
			{
				Step: "点击我tab",
				ExpectResult: &TaskExpectResult{
					Text:  "进入个人中心",
					Image: "image_path",
				},
			},
			{
				Step: "点击编辑资料",
				ExpectResult: &TaskExpectResult{
					Text:  "进入资料编辑页面",
					Image: "image_path",
				},
			},
		},
		TaskAggregationStep: "1.点击我tab 2.点击编辑资料 3.修改生日",
		AgentType:           AgentTypeOfAndroid,
		AgentConfigID:       "tt",
		Device: &Device{
			Type: DeviceTypeOfAndroid,
			Android: &AndroidDevice{
				URL: "127.0.0.1:5555",
			},
		},
		AppID:                   "tt_voice",
		AppName:                 "TT语音",
		AppDescription:          "TT语音是一款专为游戏玩家打造的语音社交软件...",
		UIComponentInstructions: "1.***目标选择器*** -- 组件结构...",
		SpecialScenarios:        "***遇到以下情况时必须流程规避处理: ** -- 弹窗页面出现弹窗...",
		Restart:                 "",
	}

	assert.NotNil(t, req)
	assert.Equal(t, "修改生日", req.TaskName)
	assert.Equal(t, AgentTypeOfAndroid, req.AgentType)
	assert.Equal(t, DeviceTypeOfAndroid, req.Device.Type)
	assert.Equal(t, "127.0.0.1:5555", req.Device.Android.URL)
	assert.Len(t, req.TaskStepByStep, 2)
}

func TestAgentType_Constants(t *testing.T) {
	assert.Equal(t, AgentType("android"), AgentTypeOfAndroid)
	assert.Equal(t, AgentType("ios"), AgentTypeOfIOS)
}

func TestDeviceType_Constants(t *testing.T) {
	assert.Equal(t, DeviceType("android"), DeviceTypeOfAndroid)
	assert.Equal(t, DeviceType("ios"), DeviceTypeOfIOS)
}

func TestTaskStatus_Constants(t *testing.T) {
	assert.Equal(t, TaskStatus("processing"), TaskStatusOfProcessing)
	assert.Equal(t, TaskStatus("succeed"), TaskStatusOfSucceed)
	assert.Equal(t, TaskStatus("failed"), TaskStatusOfFailed)
}

func TestStepStatus_Constants(t *testing.T) {
	assert.Equal(t, StepStatus("running"), StepStatusOfRunning)
	assert.Equal(t, StepStatus("completed"), StepStatusOfCompleted)
	assert.Equal(t, StepStatus("failed"), StepStatusOfFailed)
}

func TestGetTaskStatusReq_Structure(t *testing.T) {
	req := &GetTaskStatusReq{
		TaskID: "550e8400-e29b-41d4-a716-446655440000",
	}

	assert.NotNil(t, req)
	assert.Equal(t, "550e8400-e29b-41d4-a716-446655440000", req.TaskID)
}

func TestGetTaskRecordReq_Structure(t *testing.T) {
	req := &GetTaskRecordReq{
		TaskID: "550e8400-e29b-41d4-a716-446655440000",
	}

	assert.NotNil(t, req)
	assert.Equal(t, "550e8400-e29b-41d4-a716-446655440000", req.TaskID)
}

func TestStepRecord_Structure(t *testing.T) {
	record := &getTaskRecordRespData{
		Number:    1,
		StepName:  "点击我tab",
		Thought:   "需要点击底部导航栏...",
		Cost:      2.5,
		ImagePath: "screenshots/xxx.png",
		Action:    "click",
		Status:    StepStatusOfCompleted,
		StartTime: "2024-01-01T10:00:00",
		EndTime:   "2024-01-01T10:00:02",
	}

	assert.NotNil(t, record)
	assert.Equal(t, 1, record.Number)
	assert.Equal(t, "点击我tab", record.StepName)
	assert.Equal(t, StepStatusOfCompleted, record.Status)
	assert.Equal(t, 2.5, record.Cost)
}

func TestStopTaskReq_Structure(t *testing.T) {
	req := &stopTaskReq{
		TaskID: "550e8400-e29b-41d4-a716-446655440000",
	}

	assert.NotNil(t, req)
	assert.Equal(t, "550e8400-e29b-41d4-a716-446655440000", req.TaskID)
}

func TestDeleteTaskReq_Structure(t *testing.T) {
	req := &deleteTaskReq{
		TaskID: "550e8400-e29b-41d4-a716-446655440000",
	}

	assert.NotNil(t, req)
	assert.Equal(t, "550e8400-e29b-41d4-a716-446655440000", req.TaskID)
}
