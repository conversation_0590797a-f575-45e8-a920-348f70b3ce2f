package functions

import "testing"

func TestRandZH(t *testing.T) {
	type args struct {
		count int
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "<PERSON><PERSON><PERSON>(0)",
			args: args{
				count: 0,
			},
		},
		{
			name: "<PERSON><PERSON><PERSON>(1)",
			args: args{
				count: 1,
			},
		},
		{
			name: "<PERSON><PERSON><PERSON>(3)",
			args: args{
				count: 3,
			},
		},
		{
			name: "<PERSON><PERSON><PERSON>(11)",
			args: args{
				count: 11,
			},
		},
	}
	for _, tt := range tests {
		t.Run(
			tt.name, func(t *testing.T) {
				got := RandZH(tt.args.count)
				t.Logf("got: %s, %d, %d", got, len(got), len([]rune(got)))
			},
		)
	}
}
