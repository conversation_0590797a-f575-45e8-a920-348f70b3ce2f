package limiter

import (
	"context"
	"sync"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/lang"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
)

type RateLimiterV2 struct {
	logx.Logger
	ctx    context.Context
	exitCh <-chan lang.PlaceholderType
	rdb    *redis.Redis
	expire int64

	mutex    sync.RWMutex
	limiters map[string]Limiter
}

func NewRateLimiterV2(
	ctx context.Context, exitCh <-chan lang.PlaceholderType, rdb *redis.Redis, configs []Config, configV2s []ConfigV2,
	expire int64,
) *RateLimiterV2 {
	l := &RateLimiterV2{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		exitCh: exitCh,
		rdb:    rdb,
		expire: expire,

		limiters: make(map[string]Limiter, len(configs)+len(configV2s)),
	}

	l.AddRateLimiterByConfig(configs...)
	l.AddRateLimiterByConfigV2(configV2s...)

	return l
}

func (l *RateLimiterV2) AddRateLimiterByConfig(configs ...Config) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	localConfigs := make([]Config, 0, len(configs))
	distributedConfigs := make([]Config, 0, len(configs))
	for _, config := range configs {
		if config.Key == "" {
			continue
		}

		switch config.Type {
		case Local:
			localConfigs = append(localConfigs, config)
		case Distributed:
			distributedConfigs = append(distributedConfigs, config)
		default:
			continue
		}
	}

	if len(localConfigs) > 0 {
		lrl := NewLocalRateLimiter(l.ctx, l.exitCh, localConfigs)
		for _, config := range localConfigs {
			l.limiters[config.Key] = lrl
		}
		l.Infof("new a local rate limiter, configs: %s", jsonx.MarshalIgnoreError(localConfigs))
	}

	if len(distributedConfigs) > 0 {
		drl := NewDistributedRateLimiter(l.ctx, l.exitCh, l.rdb, distributedConfigs)
		drl.SetExpireTime(l.expire)
		for _, config := range distributedConfigs {
			l.limiters[config.Key] = drl
		}
		l.Infof(
			"new a distributed rate limiter, configs: %s, expire: %d",
			jsonx.MarshalIgnoreError(distributedConfigs), l.expire,
		)
	}
}

func (l *RateLimiterV2) AddRateLimiterByConfigV2(configs ...ConfigV2) {
	l.mutex.Lock()
	defer l.mutex.Unlock()

	localConfigs := make([]ConfigV2, 0, len(configs))
	for _, configV2 := range configs {
		if configV2.Key == "" {
			continue
		}

		localConfigs = append(localConfigs, configV2)
	}

	if len(localConfigs) > 0 {
		lrl := NewLocalRateLimiterV2(l.ctx, l.exitCh, localConfigs)
		for _, config := range localConfigs {
			l.limiters[config.Key] = lrl
		}
		l.Infof("new a v2 local rate limiter, configs: %s", jsonx.MarshalIgnoreError(localConfigs))
	}
}

func (l *RateLimiterV2) WaitForAllow(ctx context.Context, key string) (*Result, error) {
	l.mutex.RLock()
	rl, ok := l.limiters[key]
	l.mutex.RUnlock()
	if !ok {
		return nil, nil
	}

	return rl.BlockTake(ctx, key)
}
