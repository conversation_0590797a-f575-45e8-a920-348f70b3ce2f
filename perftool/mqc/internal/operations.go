package internal

import (
	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/mqworkerv2/consumer"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/security"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/constants"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/perftool/mqc/internal/tasks"
)

func HandleSetupOperations(svcCtx *svc.ServiceContext) error {
	// 初始化安全处理器
	security.InitSecurityHandler(svcCtx.Config.Security)

	// 注册任务
	if err := registerTasks(svcCtx); err != nil {
		return errors.Errorf("failed to register tasks, error: %+v", err)
	}

	return nil
}

func registerTasks(svcCtx *svc.ServiceContext) error {
	typename := constants.MQTaskTypePerfToolExecutePerfTest
	if svcCtx.Config.Task.TypeName != "" {
		typename = svcCtx.Config.Task.TypeName
	}

	logx.Infof(
		"register perf test task, task_id: %s, execute_id: %s, name: %s",
		svcCtx.Config.Task.TaskID, svcCtx.Config.Task.ExecuteID, typename,
	)
	return svcCtx.Consumer.RegisterHandlers(
		consumer.NewTaskHandlerOjb(
			typename, tasks.NewPerfTestTaskProcessor(svcCtx),
		),
	)
}
