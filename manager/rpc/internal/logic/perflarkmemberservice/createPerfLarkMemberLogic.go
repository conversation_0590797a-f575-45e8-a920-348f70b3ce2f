package perflarkmemberservicelogic

import (
	"context"
	"database/sql"

	"github.com/hashicorp/go-multierror"
	"github.com/pkg/errors"

	"github.com/zeromicro/go-zero/core/jsonx"
	"github.com/zeromicro/go-zero/core/mr"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"

	larkproxyclient "gitlab.ttyuyin.com/TestDevelopment/probe-backend/larkproxy/rpc/client/larkcontactuserservice"
	userclient "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/client/userservice"
	pb1 "gitlab.ttyuyin.com/TestDevelopment/qet-backend-middleware/user/rpc/pb"
)

type CreatePerfLarkMemberLogic struct {
	*BaseLogic
}

func NewCreatePerfLarkMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreatePerfLarkMemberLogic {
	return &CreatePerfLarkMemberLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// CreatePerfLarkMember 添加飞书自动拉群成员
func (l *CreatePerfLarkMemberLogic) CreatePerfLarkMember(in *pb.CreatePerfLarkMemberReq) (out *pb.CreatePerfLarkMemberResp, err error) {
	projectId := in.GetProjectId()

	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, projectId); err != nil {
		return nil, err
	}

	mr.ForEach[*pb1.UserInfo](
		func(source chan<- *pb1.UserInfo) {
			for _, item := range in.GetItems() {
				source <- item
			}
		}, func(item *pb1.UserInfo) {
			if e := l.create(projectId, item); e != nil {
				err = multierror.Append(err, e)
			}
		},
	)

	return &pb.CreatePerfLarkMemberResp{}, err
}

func (l *CreatePerfLarkMemberLogic) create(projectId string, user *pb1.UserInfo) error {
	var (
		account      = user.GetAccount()
		fullname     = user.GetFullname()
		fullDeptName = user.GetFullDeptName()
		email        = user.GetEmail()
		larkUserId   = ""
	)

	// validate the member if exist
	_, err := l.svcCtx.PerfLarkMemberModel.FindOneByProjectIdAccount(l.ctx, projectId, account)
	if err != nil {
		if !errors.Is(err, sqlx.ErrNotFound) {
			return errors.Wrapf(
				errorx.Err(errorx.DBError, err.Error()),
				"failed to find perf lark member, project_id: %s, account: %s, error: %+v",
				projectId, account, err,
			)
		}
	} else {
		return errorx.Errorf(
			errorx.AlreadyExists,
			"the perf lark member has already existed, project_id: %s, account: %s",
			projectId, account,
		)
	}

	// fetch the email if not exist
	if len(email) == 0 {
		user, err := l.svcCtx.UserRpc.ViewUser(l.ctx, &userclient.GetUserReq{Account: account})
		if err != nil {
			return err
		}
		email = user.GetUserInfo().GetEmail()
	}

	// fetch the lark user_id
	if len(email) > 0 {
		lark, err := l.svcCtx.LarkProxyRPC.GetBatchUserID(l.ctx, &larkproxyclient.GetBatchUserIDReq{Emails: []string{email}})
		if err != nil {
			return err
		}
		if len(lark.GetItems()) > 0 {
			larkUserId = lark.Items[0].UserId
		}
	}

	member := &model.PerfLarkMember{
		ProjectId: projectId,
		Account:   account,
		Fullname:  fullname,
		FullDeptName: sql.NullString{
			String: fullDeptName,
			Valid:  len(fullDeptName) > 0,
		},
		Email: sql.NullString{
			String: email,
			Valid:  len(email) > 0,
		},
		LarkUserId: sql.NullString{
			String: larkUserId,
			Valid:  len(larkUserId) > 0,
		},
		CreatedBy: l.currentUser.Account,
		UpdatedBy: l.currentUser.Account,
	}

	if err := l.svcCtx.PerfLarkMemberModel.Trans(
		l.ctx, func(ctx context.Context, session sqlx.Session) error {
			if _, err := l.svcCtx.PerfLarkMemberModel.Insert(ctx, session, member); err != nil {
				return errors.Wrapf(
					errorx.Err(errorx.DBError, err.Error()),
					"failed to insert values to table, table: %s, values: %s, error: %+v",
					l.svcCtx.PerfLarkMemberModel.Table(), jsonx.MarshalIgnoreError(member), err,
				)
			}
			return nil
		},
	); err != nil {
		return err
	}

	return nil
}
