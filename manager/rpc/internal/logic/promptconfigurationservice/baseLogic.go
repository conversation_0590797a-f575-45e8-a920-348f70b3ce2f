package promptconfigurationservicelogic

import (
	"context"
	"fmt"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/logx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/userinfo"
	qetutils "gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/common/utils"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/common"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
)

type BaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext

	currentUser *userinfo.UserInfo

	converters []qetutils.TypeConverter
}

func newBaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BaseLogic {
	return &BaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,

		currentUser: userinfo.FromContext(ctx),

		converters: []qetutils.TypeConverter{},
	}
}

func (l *BaseLogic) generateConfigID(projectID string) (string, error) {
	g := qetutils.NewUniqueIdGenerator(
		qetutils.WithGenerateFunc(utils.GenPromptConfigID), qetutils.WithIsUniqueFunc(
			func(id string) bool {
				r, err := l.svcCtx.PromptConfigModel.FindOneByProjectIdConfigId(l.ctx, projectID, id)
				if errors.Is(err, model.ErrNotFound) || r == nil {
					return true
				}
				return false
			},
		),
	)
	configID := g.Next()
	if configID == "" {
		return "", errorx.Err(
			errorx.GenerateUniqueIdFailure, "failed to generate prompt config id, please try it later",
		)
	}

	return configID, nil
}

// generateLockKey 生成分布式锁的key
func (l *BaseLogic) generateLockKey(projectID, configID string) string {
	return fmt.Sprintf("%s:%s:%s", common.ConstLockPromptConfigProjectIDConfigIDPrefix, projectID, configID)
}
