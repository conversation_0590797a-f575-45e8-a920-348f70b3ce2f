package promptconfigurationservicelogic

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/model"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type ViewPromptConfigurationLogic struct {
	*BaseLogic
}

func NewViewPromptConfigurationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ViewPromptConfigurationLogic {
	return &ViewPromptConfigurationLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

// ViewPromptConfiguration 查看Prompt配置
func (l *ViewPromptConfigurationLogic) ViewPromptConfiguration(in *pb.ViewPromptConfigurationReq) (out *pb.ViewPromptConfigurationResp, err error) {
	// validate the project_id in req
	if _, err = model.CheckProjectByProjectId(l.ctx, l.svcCtx.ProjectModel, in.GetProjectId()); err != nil {
		return nil, err
	}

	// validate the config_id in req
	config, err := model.CheckPromptConfigurationByConfigID(
		l.ctx, l.svcCtx.PromptConfigModel, in.GetProjectId(), in.GetConfigId(),
	)
	if err != nil {
		return nil, err
	}

	out = &pb.ViewPromptConfigurationResp{Configuration: &pb.PromptConfiguration{}}
	if err = utils.Copy(out.Configuration, config, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy prompt configuration to response, config: %s, error: %+v",
			jsonx.MarshalIgnoreError(config), err,
		)
	}

	return out, nil
}
