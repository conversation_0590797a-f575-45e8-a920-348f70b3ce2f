package applicationConfig

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchApplicationConfigLogic struct {
	*BaseLogic
}

func NewSearchApplicationConfigLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchApplicationConfigLogic {
	return &SearchApplicationConfigLogic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchApplicationConfigLogic) SearchApplicationConfig(req *types.SearchApplicationConfigReq) (
	resp *types.SearchApplicationConfigResp, err error,
) {
	in := &pb.SearchApplicationConfigurationReq{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerApplicationConfigurationRPC.SearchApplicationConfiguration(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchApplicationConfigResp{Items: []*types.ApplicationConfiguration{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
