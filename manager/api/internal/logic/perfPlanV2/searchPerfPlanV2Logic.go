package perfPlanV2

import (
	"context"

	"github.com/pkg/errors"
	"github.com/zeromicro/go-zero/core/jsonx"

	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/errorx"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/protobuf"
	"gitlab.ttyuyin.com/TestDevelopment/qet-backend-common/utils"

	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/svc"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/api/internal/types"
	"gitlab.ttyuyin.com/TestDevelopment/probe-backend/manager/rpc/pb"
)

type SearchPerfPlanV2Logic struct {
	*BaseLogic
}

func NewSearchPerfPlanV2Logic(ctx context.Context, svcCtx *svc.ServiceContext) *SearchPerfPlanV2Logic {
	return &SearchPerfPlanV2Logic{
		BaseLogic: newBaseLogic(ctx, svcCtx),
	}
}

func (l *SearchPerfPlanV2Logic) SearchPerfPlanV2(req *types.SearchPerfPlanV2Req) (
	resp *types.SearchPerfPlanV2Resp, err error,
) {
	in := &pb.SearchPerfPlanV2Req{}
	if err = utils.Copy(in, req, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to request, data: %s, error: %+v",
			jsonx.MarshalIgnoreError(req), err,
		)
	}

	out, err := l.svcCtx.ManagerPerfPlanV2RPC.SearchPerfPlanV2(l.ctx, in)
	if err != nil {
		return nil, err
	}

	// avoid returning null to the front end
	resp = &types.SearchPerfPlanV2Resp{Items: []*types.SearchPerfPlanV2Item{}}
	if err = utils.Copy(resp, out, l.converters...); err != nil {
		return nil, errors.Wrapf(
			errorx.Err(errorx.CopyToStructFailure, err.Error()),
			"failed to copy data to response, data: %s, error: %+v",
			protobuf.MarshalJSONIgnoreError(out), err,
		)
	}

	return resp, nil
}
