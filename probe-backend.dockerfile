FROM golang:1.18.1-alpine AS builder

LABEL stage=gobuilder

<PERSON>N<PERSON> CGO_ENABLED 0
ENV GO111MODULE="on" \
    GOPROXY="http://yw-nexus.ttyuyin.com:8081/repository/group-go/,https://goproxy.cn,direct" \
    GONOSUMDB="registry.ttyuyin.com,gitlab.ttyuyin.com"

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk update --no-cache && \
    apk add --no-cache tzdata git make && \
    git config --global http.sslVerify false

WORKDIR /build

COPY . .
RUN go mod download
RUN make all-linux

FROM python:3.10.5-alpine

RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && \
    apk update --no-cache && \
    apk add --no-cache curl jq

COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
ENV TZ Asia/Shanghai

WORKDIR /app

# apiworker
RUN mkdir /app/apiworker
COPY --from=builder /build/apiworker/bin/apiworker.linux /app/apiworker/apiworker.linux
COPY --from=builder /build/apiworker/start.sh /app/apiworker/start.sh

# account
RUN mkdir /app/account
COPY --from=builder /build/account/bin/account.linux /app/account
COPY --from=builder /build/account/start.sh /app/account/start.sh

# dispatcher
RUN mkdir /app/dispatcher
COPY --from=builder /build/dispatcher/bin/dispatcher.linux /app/dispatcher
COPY --from=builder /build/dispatcher/start.sh /app/dispatcher/start.sh

# manager
RUN mkdir /app/manager
COPY --from=builder /build/manager/bin/manager.linux /app/manager/manager.linux
COPY --from=builder /build/manager/start.sh /app/manager/start.sh

# reporter
RUN mkdir /app/reporter
COPY --from=builder /build/reporter/bin/reporter.linux /app/reporter
COPY --from=builder /build/reporter/start.sh /app/reporter/start.sh

# beat
RUN mkdir /app/beat
COPY --from=builder /build/beat/bin/beat.linux /app/beat
COPY --from=builder /build/beat/start.sh /app/beat/start.sh

# permission
RUN mkdir /app/permission
COPY --from=builder /build/permission/bin/permission.linux /app/permission
COPY --from=builder /build/permission/start.sh /app/permission/start.sh

# notifier
RUN mkdir /app/notifier
COPY --from=builder /build/notifier/bin/notifier.linux /app/notifier
COPY --from=builder /build/notifier/start.sh /app/notifier/start.sh

# user
RUN mkdir /app/user
COPY --from=builder /build/user/bin/user.linux /app/user
COPY --from=builder /build/user/start.sh /app/user/start.sh

COPY --from=builder /build/start.sh /app/start.sh
COPY --from=builder /build/health_check.sh /app/health_check.sh

ENV PATH=${PATH}:/app

ENTRYPOINT ["/app/start.sh"]

