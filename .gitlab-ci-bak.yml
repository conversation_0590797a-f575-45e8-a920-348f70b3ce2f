image: probe-runner

stages:
  - build
  - deploy
  - check
  - clear

variables:
  DOCKER_DRIVER: overlay2

before_script:
  - export VERSION=`[ -f "./VERSION" ] && cat ./VERSION || echo "0.0.1"`

build:
  stage: build
  only:
    - /^release/sprint_.*$/
    - /^release/bugfix_.*$/
  except:
    - schedule
  script:
    - docker build -f probe-backend.dockerfile -t probe-backend:$VERSION .

deploy:
  stage: deploy
  needs: [build]
  only:
    - /^release/sprint_.*$/
    - /^release/bugfix_.*$/
  except:
    - schedule
  script:
    - docker stop probe-backend && docker rm probe-backend
    - >
      docker run -d --name probe-backend
      --network host --restart=always
      -e APOLLO_AUTH_TOKEN=$APOLLO_AUTH_TOKEN
      -e APOLLO_ADDR=$APOLLO_ADDR
      --volume /home/<USER>/probe/backend/logs:/app/logs
      --volume /home/<USER>/probe/backend/account/excel_path:/app/account/excel_path
      --volume /home/<USER>/probe/backend/account/sql_path:/app/account/sql_path
      --volume /home/<USER>/probe/backend/manager/def_path:/app/manager/def_path
      probe-backend:$VERSION

health_check:
  stage: check
  needs:
    - deploy
  only:
    - /^release/sprint_.*$/
    - /^release/bugfix_.*$/
  except:
    - schedule
  script:
    - docker exec probe-backend sh /app/health_check.sh

clear_images:
  stage: clear
  needs: [deploy]
  only:
    - /^release/sprint_.*$/
    - /^release/bugfix_.*$/
  script:
    - >
      n=$(docker images -f "dangling=true" -q | wc -l); 
      if [ $n -gt 1 ]; then
        docker rmi $(docker images -f "dangling=true" -q | tail -$(expr $n - 1))
      fi
